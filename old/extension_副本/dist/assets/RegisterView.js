import{q as e,r as a,e as l,t as o,z as r,u as s,a3 as t,J as i,U as d,Y as u,D as n,a4 as c,C as v,H as m,K as p,a1 as g,V as f,v as b}from"./main.js";import{a as y}from"./index.js";const x={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},w={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},_={class:"max-w-[375px] mx-auto"},h={class:"flex items-center px-4 py-3"},k={class:"flex-1 pt-16 pb-16"},I={class:"max-w-[375px] mx-auto px-4"},S={key:0,class:"mb-4 p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-400 text-sm"},q={key:0,class:"mt-1 text-sm text-red-500"},A={key:0,class:"mt-1 text-sm text-red-500"},V={class:"flex gap-2"},C={class:"flex-1"},F={key:0,class:"mt-1 text-sm text-red-500"},U=["disabled"],j={key:0,class:"mt-1 text-sm text-red-500"},D=["disabled"],J={class:"mt-6 text-center"},O=e({__name:"RegisterView",setup(e){const O=t(),R=a({email:"",password:"",code:"",invitation_code:""}),z=a(!1),N=a(!1),E=a(0),H=a({email:"",password:"",code:"",invitation_code:""}),K=a(""),T=a(null),Y=a(null),$=a(null),B=a(null),G=()=>{H.value={email:"",password:"",code:"",invitation_code:""},K.value=""},L=e=>{const a={email:T,password:Y,code:$,invitation_code:B}[e];a.value&&setTimeout((()=>{var e;null==(e=a.value)||e.focus()}),100)},M=()=>{localStorage.setItem("registerFormData",JSON.stringify({email:R.value.email,password:R.value.password,code:R.value.code}))},P=()=>{H.value.email="",K.value="",M()},Q=()=>{H.value.password="",K.value="",M()},W=()=>{H.value.code="",K.value="",M()},X=async()=>{var e,a,l,o,r;if(G(),!R.value.email)return H.value.email="请输入邮箱",void L("email");N.value=!0;try{await y.sendCode({email:R.value.email}),(()=>{E.value=60;const e=setInterval((()=>{E.value--,E.value<=0&&clearInterval(e)}),1e3)})()}catch(s){console.error("发送验证码失败:",s),(null==(l=null==(a=null==(e=s.response)?void 0:e.data)?void 0:a.message)?void 0:l.email)?(H.value.email=s.response.data.message.email[0]||"邮箱格式错误",L("email")):(null==(r=null==(o=s.response)?void 0:o.data)?void 0:r.message)?K.value="string"==typeof s.response.data.message?s.response.data.message:"发送验证码失败，请稍后重试":K.value="发送验证码失败，请稍后重试"}finally{N.value=!1}},Z=async()=>{var e,a;G();let l=!1;if(R.value.email||(H.value.email="请输入邮箱",l||(L("email"),l=!0)),R.value.password||(H.value.password="请输入密码",l||(L("password"),l=!0)),R.value.code||(H.value.code="请输入验证码",l||(L("code"),l=!0)),R.value.invitation_code||(H.value.invitation_code="请输入邀请码",l||(L("invitation_code"),l=!0)),!l){z.value=!0;try{const e={email:R.value.email.trim(),password:R.value.password.trim(),code:R.value.code.trim(),invitation_code:R.value.invitation_code.trim()};console.log("Sending registration request with data:",e);const a=await y.register(e);console.log("Registration response:",a),localStorage.removeItem("registerFormData"),O.push("/login")}catch(o){if(console.error("Registration failed:",o),null==(a=null==(e=o.response)?void 0:e.data)?void 0:a.message){const e=o.response.data.message;if("object"==typeof e){const a={email:"email",password:"password",code:"code",invitation_code:"invitation_code"};let l=!1;Object.entries(e).forEach((([e,o])=>{const r=a[e];r?(H.value[r]=Array.isArray(o)?o[0]:o,l||(L(r),l=!0)):K.value=Array.isArray(o)?o[0]:o}))}else"string"==typeof e&&(K.value=e)}else K.value="注册失败，请检查输入信息"}finally{z.value=!1}}};return l((()=>{(()=>{const e=localStorage.getItem("registerFormData");if(e){const{email:a,password:l,code:o}=JSON.parse(e);R.value.email=a,R.value.password=l,R.value.code=o}})()})),(e,a)=>{const l=g("router-link");return b(),o("div",x,[r("header",w,[r("div",_,[r("div",h,[r("button",{onClick:a[0]||(a[0]=e=>s(O).push("/")),class:"mr-2"},a[5]||(a[5]=[r("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),a[6]||(a[6]=r("h1",{class:"text-lg font-semibold"},"注册",-1))])])]),r("main",k,[r("div",I,[a[12]||(a[12]=r("h1",{class:"text-2xl font-bold text-center mb-8"},"注册",-1)),K.value?(b(),o("div",S,d(K.value),1)):i("",!0),r("form",{onSubmit:u(Z,["prevent"]),class:"space-y-4"},[r("div",null,[a[7]||(a[7]=r("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"邮箱",-1)),n(r("input",{type:"email","onUpdate:modelValue":a[1]||(a[1]=e=>R.value.email=e),onInput:P,required:"",ref_key:"emailInput",ref:T,class:v(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",H.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入邮箱"},null,34),[[c,R.value.email]]),H.value.email?(b(),o("p",q,d(H.value.email),1)):i("",!0)]),r("div",null,[a[8]||(a[8]=r("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"密码",-1)),n(r("input",{type:"password","onUpdate:modelValue":a[2]||(a[2]=e=>R.value.password=e),onInput:Q,required:"",ref_key:"passwordInput",ref:Y,class:v(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",H.value.password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入密码"},null,34),[[c,R.value.password]]),H.value.password?(b(),o("p",A,d(H.value.password),1)):i("",!0)]),r("div",V,[r("div",C,[a[9]||(a[9]=r("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"验证码",-1)),n(r("input",{type:"text","onUpdate:modelValue":a[3]||(a[3]=e=>R.value.code=e),onInput:W,required:"",ref_key:"codeInput",ref:$,class:v(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",H.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入验证码"},null,34),[[c,R.value.code]]),H.value.code?(b(),o("p",F,d(H.value.code),1)):i("",!0)]),r("button",{type:"button",onClick:X,disabled:N.value||E.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed"},d(E.value>0?`${E.value}s后重试`:N.value?"发送中...":"获取验证码"),9,U)]),r("div",null,[a[10]||(a[10]=r("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"邀请码",-1)),n(r("input",{type:"text","onUpdate:modelValue":a[4]||(a[4]=e=>R.value.invitation_code=e),required:"",ref_key:"invitationCodeInput",ref:B,class:v(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",H.value.invitation_code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入邀请码"},null,2),[[c,R.value.invitation_code]]),H.value.invitation_code?(b(),o("p",j,d(H.value.invitation_code),1)):i("",!0)]),r("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium",disabled:z.value},d(z.value?"注册中...":"注册"),9,D)],32),r("div",J,[m(l,{to:"/login",class:"text-primary hover:underline"},{default:p((()=>a[11]||(a[11]=[f(" 已有账号？立即登录 ")]))),_:1})])])])])}}});export{O as default};
