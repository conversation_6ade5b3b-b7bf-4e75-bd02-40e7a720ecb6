import{g as A}from"./index.js";import{g as e,i as t,r,c as n,u as s,a as o,s as i,w as a,b as l,d as c,o as B,e as u,n as d,f as g,h as w,j as f,k as p,l as Q,m as h,p as C,q as U,t as F,v as y,x as v,y as m,z as b,A as E,B as H,C as I,N as x,D as K,E as L,F as D,T as S,G as T,H as M,I as O,J as k,K as _,L as R,M as P,O as V,P as N,Q as G,R as J,S as X,U as Y,V as W,W as Z,X as j,Y as z,Z as $,_ as q,$ as AA,a0 as eA,a1 as tA,a2 as rA}from"./main.js";
/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var nA=function(A,e){return(nA=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,e)};function sA(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}nA(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var oA=function(){return oA=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},oA.apply(this,arguments)};function iA(A,e,t,r){return new(t||(t=Promise))((function(e,n){function s(A){try{i(r.next(A))}catch(e){n(e)}}function o(A){try{i(r.throw(A))}catch(e){n(e)}}function i(A){var r;A.done?e(A.value):(r=A.value,r instanceof t?r:new t((function(A){A(r)}))).then(s,o)}i((r=r.apply(A,[])).next())}))}function aA(A,e){var t,r,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(n=2&s[0]?r.return:s[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,s[1])).done)return n;switch(r=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(n=o.trys,(n=n.length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=e.call(A,o)}catch(i){s=[6,i],r=0}finally{t=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}}function lA(A,e,t){if(2===arguments.length)for(var r,n=0,s=e.length;n<s;n++)!r&&n in e||(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return A.concat(r||e)}for(var cA=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e,t){return new A(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height)},A.fromDOMRectList=function(e,t){var r=Array.from(t).find((function(A){return 0!==A.width}));return r?new A(r.left+e.windowBounds.left,r.top+e.windowBounds.top,r.width,r.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),BA=function(A,e){return cA.fromClientRect(A,e.getBoundingClientRect())},uA=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e},dA=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},gA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",wA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),fA=0;fA<64;fA++)wA[gA.charCodeAt(fA)]=fA;for(var pA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",QA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),hA=0;hA<64;hA++)QA[pA.charCodeAt(hA)]=hA;for(var CA=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},UA=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),FA="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",yA="undefined"==typeof Uint8Array?[]:new Uint8Array(256),vA=0;vA<64;vA++)yA[FA.charCodeAt(vA)]=vA;var mA,bA,EA,HA,IA,xA,KA,LA,DA=10,SA=13,TA=15,MA=17,OA=18,kA=19,_A=20,RA=21,PA=22,VA=24,NA=25,GA=26,JA=27,XA=28,YA=30,WA=32,ZA=33,jA=34,zA=35,$A=37,qA=38,Ae=39,ee=40,te=42,re=[9001,65288],ne="×",se="÷",oe=(HA=function(A){var e,t,r,n,s,o=.75*A.length,i=A.length,a=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--);var l="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(o):new Array(o),c=Array.isArray(l)?l:new Uint8Array(l);for(e=0;e<i;e+=4)t=QA[A.charCodeAt(e)],r=QA[A.charCodeAt(e+1)],n=QA[A.charCodeAt(e+2)],s=QA[A.charCodeAt(e+3)],c[a++]=t<<2|r>>4,c[a++]=(15&r)<<4|n>>2,c[a++]=(3&n)<<6|63&s;return l}("KwAAAAAAAAAACA4AUD0AADAgAAACAAAAAAAIABAAGABAAEgAUABYAGAAaABgAGgAYgBqAF8AZwBgAGgAcQB5AHUAfQCFAI0AlQCdAKIAqgCyALoAYABoAGAAaABgAGgAwgDKAGAAaADGAM4A0wDbAOEA6QDxAPkAAQEJAQ8BFwF1AH0AHAEkASwBNAE6AUIBQQFJAVEBWQFhAWgBcAF4ATAAgAGGAY4BlQGXAZ8BpwGvAbUBvQHFAc0B0wHbAeMB6wHxAfkBAQIJAvEBEQIZAiECKQIxAjgCQAJGAk4CVgJeAmQCbAJ0AnwCgQKJApECmQKgAqgCsAK4ArwCxAIwAMwC0wLbAjAA4wLrAvMC+AIAAwcDDwMwABcDHQMlAy0DNQN1AD0DQQNJA0kDSQNRA1EDVwNZA1kDdQB1AGEDdQBpA20DdQN1AHsDdQCBA4kDkQN1AHUAmQOhA3UAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AKYDrgN1AHUAtgO+A8YDzgPWAxcD3gPjA+sD8wN1AHUA+wMDBAkEdQANBBUEHQQlBCoEFwMyBDgEYABABBcDSARQBFgEYARoBDAAcAQzAXgEgASIBJAEdQCXBHUAnwSnBK4EtgS6BMIEyAR1AHUAdQB1AHUAdQCVANAEYABgAGAAYABgAGAAYABgANgEYADcBOQEYADsBPQE/AQEBQwFFAUcBSQFLAU0BWQEPAVEBUsFUwVbBWAAYgVgAGoFcgV6BYIFigWRBWAAmQWfBaYFYABgAGAAYABgAKoFYACxBbAFuQW6BcEFwQXHBcEFwQXPBdMF2wXjBeoF8gX6BQIGCgYSBhoGIgYqBjIGOgZgAD4GRgZMBmAAUwZaBmAAYABgAGAAYABgAGAAYABgAGAAYABgAGIGYABpBnAGYABgAGAAYABgAGAAYABgAGAAYAB4Bn8GhQZgAGAAYAB1AHcDFQSLBmAAYABgAJMGdQA9A3UAmwajBqsGqwaVALMGuwbDBjAAywbSBtIG1QbSBtIG0gbSBtIG0gbdBuMG6wbzBvsGAwcLBxMHAwcbByMHJwcsBywHMQcsB9IGOAdAB0gHTgfSBkgHVgfSBtIG0gbSBtIG0gbSBtIG0gbSBiwHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAdgAGAALAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAdbB2MHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsB2kH0gZwB64EdQB1AHUAdQB1AHUAdQB1AHUHfQdgAIUHjQd1AHUAlQedB2AAYAClB6sHYACzB7YHvgfGB3UAzgfWBzMB3gfmB1EB7gf1B/0HlQENAQUIDQh1ABUIHQglCBcDLQg1CD0IRQhNCEEDUwh1AHUAdQBbCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIaQhjCGQIZQhmCGcIaAhpCGMIZAhlCGYIZwhoCGkIYwhkCGUIZghnCGgIcAh3CHoIMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwAIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIgggwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAALAcsBywHLAcsBywHLAcsBywHLAcsB4oILAcsB44I0gaWCJ4Ipgh1AHUAqgiyCHUAdQB1AHUAdQB1AHUAdQB1AHUAtwh8AXUAvwh1AMUIyQjRCNkI4AjoCHUAdQB1AO4I9gj+CAYJDgkTCS0HGwkjCYIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiCCIIIggiAAIAAAAFAAYABgAGIAXwBgAHEAdQBFAJUAogCyAKAAYABgAEIA4ABGANMA4QDxAMEBDwE1AFwBLAE6AQEBUQF4QkhCmEKoQrhCgAHIQsAB0MLAAcABwAHAAeDC6ABoAHDCwMMAAcABwAHAAdDDGMMAAcAB6MM4wwjDWMNow3jDaABoAGgAaABoAGgAaABoAGgAaABoAGgAaABoAGgAaABoAGgAaABoAEjDqABWw6bDqABpg6gAaABoAHcDvwOPA+gAaABfA/8DvwO/A78DvwO/A78DvwO/A78DvwO/A78DvwO/A78DvwO/A78DvwO/A78DvwO/A78DvwO/A78DpcPAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcAB9cPKwkyCToJMAB1AHUAdQBCCUoJTQl1AFUJXAljCWcJawkwADAAMAAwAHMJdQB2CX4JdQCECYoJjgmWCXUAngkwAGAAYABxAHUApgn3A64JtAl1ALkJdQDACTAAMAAwADAAdQB1AHUAdQB1AHUAdQB1AHUAowYNBMUIMAAwADAAMADICcsJ0wnZCRUE4QkwAOkJ8An4CTAAMAB1AAAKvwh1AAgKDwoXCh8KdQAwACcKLgp1ADYKqAmICT4KRgowADAAdQB1AE4KMAB1AFYKdQBeCnUAZQowADAAMAAwADAAMAAwADAAMAAVBHUAbQowADAAdQC5CXUKMAAwAHwBxAijBogEMgF9CoQKiASMCpQKmgqIBKIKqgquCogEDQG2Cr4KxgrLCjAAMADTCtsKCgHjCusK8Qr5CgELMAAwADAAMAB1AIsECQsRC3UANAEZCzAAMAAwADAAMAB1ACELKQswAHUANAExCzkLdQBBC0kLMABRC1kLMAAwADAAMAAwADAAdQBhCzAAMAAwAGAAYABpC3ELdwt/CzAAMACHC4sLkwubC58Lpwt1AK4Ltgt1APsDMAAwADAAMAAwADAAMAAwAL4LwwvLC9IL1wvdCzAAMADlC+kL8Qv5C/8LSQswADAAMAAwADAAMAAwADAAMAAHDDAAMAAwADAAMAAODBYMHgx1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1ACYMMAAwADAAdQB1AHUALgx1AHUAdQB1AHUAdQA2DDAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwAHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AD4MdQBGDHUAdQB1AHUAdQB1AEkMdQB1AHUAdQB1AFAMMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwAHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQBYDHUAdQB1AF8MMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUA+wMVBGcMMAAwAHwBbwx1AHcMfwyHDI8MMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAYABgAJcMMAAwADAAdQB1AJ8MlQClDDAAMACtDCwHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsB7UMLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHdQB1AHUAdQB1AHUAdQB1AHUAdQB1AHUAdQB1AA0EMAC9DDAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAsBywHLAcsBywHLAcsBywHLQcwAMEMyAwsBywHLAcsBywHLAcsBywHLAcsBywHzAwwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwAHUAdQB1ANQM2QzhDDAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMABgAGAAYABgAGAAYABgAOkMYADxDGAA+AwADQYNYABhCWAAYAAODTAAMAAwADAAFg1gAGAAHg37AzAAMAAwADAAYABgACYNYAAsDTQNPA1gAEMNPg1LDWAAYABgAGAAYABgAGAAYABgAGAAUg1aDYsGVglhDV0NcQBnDW0NdQ15DWAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAlQCBDZUAiA2PDZcNMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAnw2nDTAAMAAwADAAMAAwAHUArw23DTAAMAAwADAAMAAwADAAMAAwADAAMAB1AL8NMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAB1AHUAdQB1AHUAdQDHDTAAYABgAM8NMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAA1w11ANwNMAAwAD0B5A0wADAAMAAwADAAMADsDfQN/A0EDgwOFA4wABsOMAAwADAAMAAwADAAMAAwANIG0gbSBtIG0gbSBtIG0gYjDigOwQUuDsEFMw7SBjoO0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIGQg5KDlIOVg7SBtIGXg5lDm0OdQ7SBtIGfQ6EDooOjQ6UDtIGmg6hDtIG0gaoDqwO0ga0DrwO0gZgAGAAYADEDmAAYAAkBtIGzA5gANIOYADaDokO0gbSBt8O5w7SBu8O0gb1DvwO0gZgAGAAxA7SBtIG0gbSBtIGYABgAGAAYAAED2AAsAUMD9IG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIGFA8sBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAccD9IGLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHJA8sBywHLAcsBywHLAccDywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywPLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAc0D9IG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIGLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAccD9IG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIGFA8sBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHLAcsBywHPA/SBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gbSBtIG0gYUD0QPlQCVAJUAMAAwADAAMACVAJUAlQCVAJUAlQCVAEwPMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAA//8EAAQABAAEAAQABAAEAAQABAANAAMAAQABAAIABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQACgATABcAHgAbABoAHgAXABYAEgAeABsAGAAPABgAHABLAEsASwBLAEsASwBLAEsASwBLABgAGAAeAB4AHgATAB4AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQABYAGwASAB4AHgAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAWAA0AEQAeAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAAQABAAEAAQABAAFAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAJABYAGgAbABsAGwAeAB0AHQAeAE8AFwAeAA0AHgAeABoAGwBPAE8ADgBQAB0AHQAdAE8ATwAXAE8ATwBPABYAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAFAAUABQAFAAUABQAFAAUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAFAAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAeAB4AHgAeAFAATwBAAE8ATwBPAEAATwBQAFAATwBQAB4AHgAeAB4AHgAeAB0AHQAdAB0AHgAdAB4ADgBQAFAAUABQAFAAHgAeAB4AHgAeAB4AHgBQAB4AUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4ABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAJAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAkACQAJAAkACQAJAAkABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAeAB4AHgAeAFAAHgAeAB4AKwArAFAAUABQAFAAGABQACsAKwArACsAHgAeAFAAHgBQAFAAUAArAFAAKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4ABAAEAAQABAAEAAQABAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAUAAeAB4AHgAeAB4AHgBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAYAA0AKwArAB4AHgAbACsABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQADQAEAB4ABAAEAB4ABAAEABMABAArACsAKwArACsAKwArACsAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAKwArACsAKwBWAFYAVgBWAB4AHgArACsAKwArACsAKwArACsAKwArACsAHgAeAB4AHgAeAB4AHgAeAB4AGgAaABoAGAAYAB4AHgAEAAQABAAEAAQABAAEAAQABAAEAAQAEwAEACsAEwATAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABABLAEsASwBLAEsASwBLAEsASwBLABoAGQAZAB4AUABQAAQAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQABMAUAAEAAQABAAEAAQABAAEAB4AHgAEAAQABAAEAAQABABQAFAABAAEAB4ABAAEAAQABABQAFAASwBLAEsASwBLAEsASwBLAEsASwBQAFAAUAAeAB4AUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwAeAFAABABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAFAAKwArACsAKwArACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQAUABQAB4AHgAYABMAUAArACsABAAbABsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAFAABAAEAAQABAAEAFAABAAEAAQAUAAEAAQABAAEAAQAKwArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAArACsAHgArAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwArACsAKwArACsAKwArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAB4ABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAUAAEAAQABAAEAAQABAAEAFAAUABQAFAAUABQAFAAUABQAFAABAAEAA0ADQBLAEsASwBLAEsASwBLAEsASwBLAB4AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAArAFAAUABQAFAAUABQAFAAUAArACsAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUAArACsAKwBQAFAAUABQACsAKwAEAFAABAAEAAQABAAEAAQABAArACsABAAEACsAKwAEAAQABABQACsAKwArACsAKwArACsAKwAEACsAKwArACsAUABQACsAUABQAFAABAAEACsAKwBLAEsASwBLAEsASwBLAEsASwBLAFAAUAAaABoAUABQAFAAUABQAEwAHgAbAFAAHgAEACsAKwAEAAQABAArAFAAUABQAFAAUABQACsAKwArACsAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUABQACsAUABQACsAUABQACsAKwAEACsABAAEAAQABAAEACsAKwArACsABAAEACsAKwAEAAQABAArACsAKwAEACsAKwArACsAKwArACsAUABQAFAAUAArAFAAKwArACsAKwArACsAKwBLAEsASwBLAEsASwBLAEsASwBLAAQABABQAFAAUAAEAB4AKwArACsAKwArACsAKwArACsAKwAEAAQABAArAFAAUABQAFAAUABQAFAAUABQACsAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUABQACsAUABQAFAAUABQACsAKwAEAFAABAAEAAQABAAEAAQABAAEACsABAAEAAQAKwAEAAQABAArACsAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAABAAEACsAKwBLAEsASwBLAEsASwBLAEsASwBLAB4AGwArACsAKwArACsAKwArAFAABAAEAAQABAAEAAQAKwAEAAQABAArAFAAUABQAFAAUABQAFAAUAArACsAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAArACsABAAEACsAKwAEAAQABAArACsAKwArACsAKwArAAQABAAEACsAKwArACsAUABQACsAUABQAFAABAAEACsAKwBLAEsASwBLAEsASwBLAEsASwBLAB4AUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArAAQAUAArAFAAUABQAFAAUABQACsAKwArAFAAUABQACsAUABQAFAAUAArACsAKwBQAFAAKwBQACsAUABQACsAKwArAFAAUAArACsAKwBQAFAAUAArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArAAQABAAEAAQABAArACsAKwAEAAQABAArAAQABAAEAAQAKwArAFAAKwArACsAKwArACsABAArACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAUABQAFAAHgAeAB4AHgAeAB4AGwAeACsAKwArACsAKwAEAAQABAAEAAQAUABQAFAAUABQAFAAUABQACsAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAUAAEAAQABAAEAAQABAAEACsABAAEAAQAKwAEAAQABAAEACsAKwArACsAKwArACsABAAEACsAUABQAFAAKwArACsAKwArAFAAUAAEAAQAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAKwAOAFAAUABQAFAAUABQAFAAHgBQAAQABAAEAA4AUABQAFAAUABQAFAAUABQACsAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAKwArAAQAUAAEAAQABAAEAAQABAAEACsABAAEAAQAKwAEAAQABAAEACsAKwArACsAKwArACsABAAEACsAKwArACsAKwArACsAUAArAFAAUAAEAAQAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwBQAFAAKwArACsAKwArACsAKwArACsAKwArACsAKwAEAAQABAAEAFAAUABQAFAAUABQAFAAUABQACsAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAFAABAAEAAQABAAEAAQABAArAAQABAAEACsABAAEAAQABABQAB4AKwArACsAKwBQAFAAUAAEAFAAUABQAFAAUABQAFAAUABQAFAABAAEACsAKwBLAEsASwBLAEsASwBLAEsASwBLAFAAUABQAFAAUABQAFAAUABQABoAUABQAFAAUABQAFAAKwAEAAQABAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQACsAUAArACsAUABQAFAAUABQAFAAUAArACsAKwAEACsAKwArACsABAAEAAQABAAEAAQAKwAEACsABAAEAAQABAAEAAQABAAEACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArAAQABAAeACsAKwArACsAKwArACsAKwArACsAKwArAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXAAqAFwAXAAqACoAKgAqACoAKgAqACsAKwArACsAGwBcAFwAXABcAFwAXABcACoAKgAqACoAKgAqACoAKgAeAEsASwBLAEsASwBLAEsASwBLAEsADQANACsAKwArACsAKwBcAFwAKwBcACsAXABcAFwAXABcACsAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcACsAXAArAFwAXABcAFwAXABcAFwAXABcAFwAKgBcAFwAKgAqACoAKgAqACoAKgAqACoAXAArACsAXABcAFwAXABcACsAXAArACoAKgAqACoAKgAqACsAKwBLAEsASwBLAEsASwBLAEsASwBLACsAKwBcAFwAXABcAFAADgAOAA4ADgAeAA4ADgAJAA4ADgANAAkAEwATABMAEwATAAkAHgATAB4AHgAeAAQABAAeAB4AHgAeAB4AHgBLAEsASwBLAEsASwBLAEsASwBLAFAAUABQAFAAUABQAFAAUABQAFAADQAEAB4ABAAeAAQAFgARABYAEQAEAAQAUABQAFAAUABQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQADQAEAAQABAAEAAQADQAEAAQAUABQAFAAUABQAAQABAAEAAQABAAEAAQABAAEAAQABAArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAArAA0ADQAeAB4AHgAeAB4AHgAEAB4AHgAeAB4AHgAeACsAHgAeAA4ADgANAA4AHgAeAB4AHgAeAAkACQArACsAKwArACsAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgBcAEsASwBLAEsASwBLAEsASwBLAEsADQANAB4AHgAeAB4AXABcAFwAXABcAFwAKgAqACoAKgBcAFwAXABcACoAKgAqAFwAKgAqACoAXABcACoAKgAqACoAKgAqACoAXABcAFwAKgAqACoAKgBcAFwAXABcAFwAXABcAFwAXABcAFwAXABcACoAKgAqACoAKgAqACoAKgAqACoAKgAqAFwAKgBLAEsASwBLAEsASwBLAEsASwBLACoAKgAqACoAKgAqAFAAUABQAFAAUABQACsAUAArACsAKwArACsAUAArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgBQAFAAUABQAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFAAUABQAFAAUABQAFAAUABQACsAUABQAFAAUAArACsAUABQAFAAUABQAFAAUAArAFAAKwBQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAKwArAFAAUABQAFAAUABQAFAAKwBQACsAUABQAFAAUAArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsABAAEAAQAHgANAB4AHgAeAB4AHgAeAB4AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUAArACsADQBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAANAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAWABEAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAA0ADQANAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAAQABAAEACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAANAA0AKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUAArAAQABAArACsAKwArACsAKwArACsAKwArACsAKwBcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqAA0ADQAVAFwADQAeAA0AGwBcACoAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwAeAB4AEwATAA0ADQAOAB4AEwATAB4ABAAEAAQACQArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAFAAUABQAFAAUAAEAAQAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQAUAArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAArACsAKwArAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwArACsAHgArACsAKwATABMASwBLAEsASwBLAEsASwBLAEsASwBcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXAArACsAXABcAFwAXABcACsAKwArACsAKwArACsAKwArACsAKwBcAFwAXABcAFwAXABcAFwAXABcAFwAXAArACsAKwArAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAXAArACsAKwAqACoAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAArACsAHgAeAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcACoAKgAqACoAKgAqACoAKgAqACoAKwAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKwArAAQASwBLAEsASwBLAEsASwBLAEsASwArACsAKwArACsAKwBLAEsASwBLAEsASwBLAEsASwBLACsAKwArACsAKwArACoAKgAqACoAKgAqACoAXAAqACoAKgAqACoAKgArACsABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsABAAEAAQABAAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABABQAFAAUABQAFAAUABQACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwANAA0AHgANAA0ADQANAB4AHgAeAB4AHgAeAB4AHgAeAB4ABAAEAAQABAAEAAQABAAEAAQAHgAeAB4AHgAeAB4AHgAeAB4AKwArACsABAAEAAQAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABABQAFAASwBLAEsASwBLAEsASwBLAEsASwBQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwArACsAKwArACsAKwAeAB4AHgAeAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwArAA0ADQANAA0ADQBLAEsASwBLAEsASwBLAEsASwBLACsAKwArAFAAUABQAEsASwBLAEsASwBLAEsASwBLAEsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAA0ADQBQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUAAeAB4AHgAeAB4AHgAeAB4AKwArACsAKwArACsAKwArAAQABAAEAB4ABAAEAAQABAAEAAQABAAEAAQABAAEAAQABABQAFAAUABQAAQAUABQAFAAUABQAFAABABQAFAABAAEAAQAUAArACsAKwArACsABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsABAAEAAQABAAEAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwArAFAAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAKwBQACsAUAArAFAAKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACsAKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArAB4AHgAeAB4AHgAeAB4AHgBQAB4AHgAeAFAAUABQACsAHgAeAB4AHgAeAB4AHgAeAB4AHgBQAFAAUABQACsAKwAeAB4AHgAeAB4AHgArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwArAFAAUABQACsAHgAeAB4AHgAeAB4AHgAOAB4AKwANAA0ADQANAA0ADQANAAkADQANAA0ACAAEAAsABAAEAA0ACQANAA0ADAAdAB0AHgAXABcAFgAXABcAFwAWABcAHQAdAB4AHgAUABQAFAANAAEAAQAEAAQABAAEAAQACQAaABoAGgAaABoAGgAaABoAHgAXABcAHQAVABUAHgAeAB4AHgAeAB4AGAAWABEAFQAVABUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4ADQAeAA0ADQANAA0AHgANAA0ADQAHAB4AHgAeAB4AKwAEAAQABAAEAAQABAAEAAQABAAEAFAAUAArACsATwBQAFAAUABQAFAAHgAeAB4AFgARAE8AUABPAE8ATwBPAFAAUABQAFAAUAAeAB4AHgAWABEAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArABsAGwAbABsAGwAbABsAGgAbABsAGwAbABsAGwAbABsAGwAbABsAGwAbABsAGgAbABsAGwAbABoAGwAbABoAGwAbABsAGwAbABsAGwAbABsAGwAbABsAGwAbABsAGwAbAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAHgAeAFAAGgAeAB0AHgBQAB4AGgAeAB4AHgAeAB4AHgAeAB4AHgBPAB4AUAAbAB4AHgBQAFAAUABQAFAAHgAeAB4AHQAdAB4AUAAeAFAAHgBQAB4AUABPAFAAUAAeAB4AHgAeAB4AHgAeAFAAUABQAFAAUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAFAAHgBQAFAAUABQAE8ATwBQAFAAUABQAFAATwBQAFAATwBQAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAFAAUABQAFAATwBPAE8ATwBPAE8ATwBPAE8ATwBQAFAAUABQAFAAUABQAFAAUAAeAB4AUABQAFAAUABPAB4AHgArACsAKwArAB0AHQAdAB0AHQAdAB0AHQAdAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB0AHgAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB4AHQAdAB4AHgAeAB0AHQAeAB4AHQAeAB4AHgAdAB4AHQAbABsAHgAdAB4AHgAeAB4AHQAeAB4AHQAdAB0AHQAeAB4AHQAeAB0AHgAdAB0AHQAdAB0AHQAeAB0AHgAeAB4AHgAeAB0AHQAdAB0AHgAeAB4AHgAdAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB4AHgAeAB0AHgAeAB4AHgAeAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB0AHgAeAB0AHQAdAB0AHgAeAB0AHQAeAB4AHQAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB0AHQAeAB4AHQAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHQAeAB4AHgAdAB4AHgAeAB4AHgAeAB4AHQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AFAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeABYAEQAWABEAHgAeAB4AHgAeAB4AHQAeAB4AHgAeAB4AHgAeACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAWABEAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AJQAlACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAFAAHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHgAeAB4AHgAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAeAB4AHQAdAB0AHQAeAB4AHgAeAB4AHgAeAB4AHgAeAB0AHQAeAB0AHQAdAB0AHQAdAB0AHgAeAB4AHgAeAB4AHgAeAB0AHQAeAB4AHQAdAB4AHgAeAB4AHQAdAB4AHgAeAB4AHQAdAB0AHgAeAB0AHgAeAB0AHQAdAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB0AHQAdAB4AHgAeAB4AHgAeAB4AHgAeAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAlACUAJQAlAB4AHQAdAB4AHgAdAB4AHgAeAB4AHQAdAB4AHgAeAB4AJQAlAB0AHQAlAB4AJQAlACUAIAAlACUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAlACUAJQAeAB4AHgAeAB0AHgAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB0AHgAdAB0AHQAeAB0AJQAdAB0AHgAdAB0AHgAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHQAdAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAlACUAJQAlACUAJQAlACUAJQAlACUAJQAdAB0AHQAdACUAHgAlACUAJQAdACUAJQAdAB0AHQAlACUAHQAdACUAHQAdACUAJQAlAB4AHQAeAB4AHgAeAB0AHQAlAB0AHQAdAB0AHQAdACUAJQAlACUAJQAdACUAJQAgACUAHQAdACUAJQAlACUAJQAlACUAJQAeAB4AHgAlACUAIAAgACAAIAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB0AHgAeAB4AFwAXABcAFwAXABcAHgATABMAJQAeAB4AHgAWABEAFgARABYAEQAWABEAFgARABYAEQAWABEATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeABYAEQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAWABEAFgARABYAEQAWABEAFgARAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AFgARABYAEQAWABEAFgARABYAEQAWABEAFgARABYAEQAWABEAFgARABYAEQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAWABEAFgARAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AFgARAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAdAB0AHQAdAB0AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AUABQAFAAUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAEAAQABAAeAB4AKwArACsAKwArABMADQANAA0AUAATAA0AUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAUAANACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQACsAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXAA0ADQANAA0ADQANAA0ADQAeAA0AFgANAB4AHgAXABcAHgAeABcAFwAWABEAFgARABYAEQAWABEADQANAA0ADQATAFAADQANAB4ADQANAB4AHgAeAB4AHgAMAAwADQANAA0AHgANAA0AFgANAA0ADQANAA0ADQANAA0AHgANAB4ADQANAB4AHgAeACsAKwArACsAKwArACsAKwArACsAKwArACsAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACsAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAKwArACsAKwArACsAKwArACsAKwArACsAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAlACUAJQAlACUAJQAlACUAJQAlACUAJQArACsAKwArAA0AEQARACUAJQBHAFcAVwAWABEAFgARABYAEQAWABEAFgARACUAJQAWABEAFgARABYAEQAWABEAFQAWABEAEQAlAFcAVwBXAFcAVwBXAFcAVwBXAAQABAAEAAQABAAEACUAVwBXAFcAVwA2ACUAJQBXAFcAVwBHAEcAJQAlACUAKwBRAFcAUQBXAFEAVwBRAFcAUQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFEAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBRAFcAUQBXAFEAVwBXAFcAVwBXAFcAUQBXAFcAVwBXAFcAVwBRAFEAKwArAAQABAAVABUARwBHAFcAFQBRAFcAUQBXAFEAVwBRAFcAUQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFEAVwBRAFcAUQBXAFcAVwBXAFcAVwBRAFcAVwBXAFcAVwBXAFEAUQBXAFcAVwBXABUAUQBHAEcAVwArACsAKwArACsAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAKwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAKwAlACUAVwBXAFcAVwAlACUAJQAlACUAJQAlACUAJQAlACsAKwArACsAKwArACsAKwArACsAKwArAFEAUQBRAFEAUQBRAFEAUQBRAFEAUQBRAFEAUQBRAFEAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQArAFcAVwBXAFcAVwBXAFcAVwBXAFcAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQBPAE8ATwBPAE8ATwBPAE8AJQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXACUAJQAlAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAEcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAKwArACsAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAADQATAA0AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABLAEsASwBLAEsASwBLAEsASwBLAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAFAABAAEAAQABAAeAAQABAAEAAQABAAEAAQABAAEAAQAHgBQAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AUABQAAQABABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAeAA0ADQANAA0ADQArACsAKwArACsAKwArACsAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAFAAUABQAFAAUABQAFAAUABQAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AUAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgBQAB4AHgAeAB4AHgAeAFAAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAHgAeAB4AHgAeAB4AHgAeAB4AKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAeAB4AUABQAFAAUABQAFAAUABQAFAAUABQAAQAUABQAFAABABQAFAAUABQAAQAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAAeAB4AHgAeAAQAKwArACsAUABQAFAAUABQAFAAHgAeABoAHgArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAADgAOABMAEwArACsAKwArACsAKwArACsABAAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAAEACsAKwArACsAKwArACsAKwANAA0ASwBLAEsASwBLAEsASwBLAEsASwArACsAKwArACsAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABABQAFAAUABQAFAAUAAeAB4AHgBQAA4AUABQAAQAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAA0ADQBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAKwArACsAKwArACsAKwArACsAKwArAB4AWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYAFgAWABYACsAKwArAAQAHgAeAB4AHgAeAB4ADQANAA0AHgAeAB4AHgArAFAASwBLAEsASwBLAEsASwBLAEsASwArACsAKwArAB4AHgBcAFwAXABcAFwAKgBcAFwAXABcAFwAXABcAFwAXABcAEsASwBLAEsASwBLAEsASwBLAEsAXABcAFwAXABcACsAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsAKwArACsAKwArACsAKwArAFAAUABQAAQAUABQAFAAUABQAFAAUABQAAQABAArACsASwBLAEsASwBLAEsASwBLAEsASwArACsAHgANAA0ADQBcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAKgAqACoAXAAqACoAKgBcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXAAqAFwAKgAqACoAXABcACoAKgBcAFwAXABcAFwAKgAqAFwAKgBcACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFwAXABcACoAKgBQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAA0ADQBQAFAAUAAEAAQAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUAArACsAUABQAFAAUABQAFAAKwArAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgAeACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQADQAEAAQAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAVABVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBUAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVAFUAVQBVACsAKwArACsAKwArACsAKwArACsAKwArAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAWQBZAFkAKwArACsAKwBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAKwArACsAKwAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYABgAGAAYAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXACUAJQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAJQAlACUAJQAlACUAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAKwArACsAKwArAFYABABWAFYAVgBWAFYAVgBWAFYAVgBWAB4AVgBWAFYAVgBWAFYAVgBWAFYAVgBWAFYAVgArAFYAVgBWAFYAVgArAFYAKwBWAFYAKwBWAFYAKwBWAFYAVgBWAFYAVgBWAFYAVgBWAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAEQAWAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUAAaAB4AKwArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAGAARABEAGAAYABMAEwAWABEAFAArACsAKwArACsAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACUAJQAlACUAJQAWABEAFgARABYAEQAWABEAFgARABYAEQAlACUAFgARACUAJQAlACUAJQAlACUAEQAlABEAKwAVABUAEwATACUAFgARABYAEQAWABEAJQAlACUAJQAlACUAJQAlACsAJQAbABoAJQArACsAKwArAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArAAcAKwATACUAJQAbABoAJQAlABYAEQAlACUAEQAlABEAJQBXAFcAVwBXAFcAVwBXAFcAVwBXABUAFQAlACUAJQATACUAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXABYAJQARACUAJQAlAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwAWACUAEQAlABYAEQARABYAEQARABUAVwBRAFEAUQBRAFEAUQBRAFEAUQBRAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAEcARwArACsAVwBXAFcAVwBXAFcAKwArAFcAVwBXAFcAVwBXACsAKwBXAFcAVwBXAFcAVwArACsAVwBXAFcAKwArACsAGgAbACUAJQAlABsAGwArAB4AHgAeAB4AHgAeAB4AKwArACsAKwArACsAKwArACsAKwAEAAQABAAQAB0AKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsADQANAA0AKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArAB4AHgAeAB4AHgAeAB4AHgAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgBQAFAAHgAeAB4AKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAAQAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAA0AUABQAFAAUAArACsAKwArAFAAUABQAFAAUABQAFAAUAANAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwAeACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAKwArAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUAArACsAKwBQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwANAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAeAB4AUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUAArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArAA0AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAUABQAFAAUABQAAQABAAEACsABAAEACsAKwArACsAKwAEAAQABAAEAFAAUABQAFAAKwBQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArAAQABAAEACsAKwArACsABABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAA0ADQANAA0ADQANAA0ADQAeACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAeAFAAUABQAFAAUABQAFAAUAAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAArACsAKwArAFAAUABQAFAAUAANAA0ADQANAA0ADQAUACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsADQANAA0ADQANAA0ADQBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAB4AHgAeAB4AKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArAFAAUABQAFAAUABQAAQABAAEAAQAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUAArAAQABAANACsAKwBQAFAAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAAQABAAEAAQABAAEAAQABAAEAAQABABQAFAAUABQAB4AHgAeAB4AHgArACsAKwArACsAKwAEAAQABAAEAAQABAAEAA0ADQAeAB4AHgAeAB4AKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsABABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAQABAAEAAQABAAEAAQABAAEAAQABAAeAB4AHgANAA0ADQANACsAKwArACsAKwArACsAKwArACsAKwAeACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwArACsAKwBLAEsASwBLAEsASwBLAEsASwBLACsAKwArACsAKwArAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEACsASwBLAEsASwBLAEsASwBLAEsASwANAA0ADQANAFAABAAEAFAAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAeAA4AUAArACsAKwArACsAKwArACsAKwAEAFAAUABQAFAADQANAB4ADQAEAAQABAAEAB4ABAAEAEsASwBLAEsASwBLAEsASwBLAEsAUAAOAFAADQANAA0AKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAANAA0AHgANAA0AHgAEACsAUABQAFAAUABQAFAAUAArAFAAKwBQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAA0AKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsABAAEAAQABAArAFAAUABQAFAAUABQAFAAUAArACsAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUABQACsAUABQAFAAUABQACsABAAEAFAABAAEAAQABAAEAAQABAArACsABAAEACsAKwAEAAQABAArACsAUAArACsAKwArACsAKwAEACsAKwArACsAKwBQAFAAUABQAFAABAAEACsAKwAEAAQABAAEAAQABAAEACsAKwArAAQABAAEAAQABAArACsAKwArACsAKwArACsAKwArACsABAAEAAQABAAEAAQABABQAFAAUABQAA0ADQANAA0AHgBLAEsASwBLAEsASwBLAEsASwBLAA0ADQArAB4ABABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAEAAQABAAEAFAAUAAeAFAAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAArACsABAAEAAQABAAEAAQABAAEAAQADgANAA0AEwATAB4AHgAeAA0ADQANAA0ADQANAA0ADQANAA0ADQANAA0ADQANAFAAUABQAFAABAAEACsAKwAEAA0ADQAeAFAAKwArACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAFAAKwArACsAKwArACsAKwBLAEsASwBLAEsASwBLAEsASwBLACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAXABcAFwAKwArACoAKgAqACoAKgAqACoAKgAqACoAKgAqACoAKgAqACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwBcAFwADQANAA0AKgBQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAeACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwBQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAKwArAFAAKwArAFAAUABQAFAAUABQAFAAUAArAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQAKwAEAAQAKwArAAQABAAEAAQAUAAEAFAABAAEAA0ADQANACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAArACsABAAEAAQABAAEAAQABABQAA4AUAAEACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAABAAEAAQABAAEAAQABAAEAAQABABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAFAABAAEAAQABAAOAB4ADQANAA0ADQAOAB4ABAArACsAKwArACsAKwArACsAUAAEAAQABAAEAAQABAAEAAQABAAEAAQAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAA0ADQANAFAADgAOAA4ADQANACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAEAAQABAAEACsABAAEAAQABAAEAAQABAAEAFAADQANAA0ADQANACsAKwArACsAKwArACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwAOABMAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQACsAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAArACsAKwAEACsABAAEACsABAAEAAQABAAEAAQABABQAAQAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAUABQAFAAUABQAFAAKwBQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQAKwAEAAQAKwAEAAQABAAEAAQAUAArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAABAAEAAQABAAeAB4AKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAB4AHgAeAB4AHgAeAB4AHgAaABoAGgAaAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwArACsAKwArACsAKwArAA0AUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsADQANAA0ADQANACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAASABIAEgAQwBDAEMAUABQAFAAUABDAFAAUABQAEgAQwBIAEMAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAASABDAEMAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwAJAAkACQAJAAkACQAJABYAEQArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABIAEMAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwANAA0AKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArAAQABAAEAAQABAANACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEAA0ADQANAB4AHgAeAB4AHgAeAFAAUABQAFAADQAeACsAKwArACsAKwArACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwArAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAANAA0AHgAeACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwAEAFAABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAKwArACsAKwArACsAKwAEAAQABAAEAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAARwBHABUARwAJACsAKwArACsAKwArACsAKwArACsAKwAEAAQAKwArACsAKwArACsAKwArACsAKwArACsAKwArAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXACsAKwArACsAKwArACsAKwBXAFcAVwBXAFcAVwBXAFcAVwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUQBRAFEAKwArACsAKwArACsAKwArACsAKwArACsAKwBRAFEAUQBRACsAKwArACsAKwArACsAKwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUAArACsAHgAEAAQADQAEAAQABAAEACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwArACsAKwArAB4AHgAeAB4AHgAeAB4AKwArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAAQABAAEAAQABAAeAB4AHgAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAB4AHgAEAAQABAAEAAQABAAEAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4ABAAEAAQABAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4ABAAEAAQAHgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwArACsAKwArACsAKwArACsAKwArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwArACsAKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwBQAFAAKwArAFAAKwArAFAAUAArACsAUABQAFAAUAArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACsAUAArAFAAUABQAFAAUABQAFAAKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwBQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAHgAeAFAAUABQAFAAUAArAFAAKwArACsAUABQAFAAUABQAFAAUAArAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAHgBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgBQAFAAUABQAFAAUABQAFAAUABQAFAAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAB4AHgAeAB4AHgAeAB4AHgAeACsAKwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAEsASwBLAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAeAB4AHgAeAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAeAB4AHgAeAB4AHgAeAB4ABAAeAB4AHgAeAB4AHgAeAB4AHgAeAAQAHgAeAA0ADQANAA0AHgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAEAAQABAAEAAQAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAAQABAAEAAQABAAEAAQAKwAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAKwArAAQABAAEAAQABAAEAAQAKwAEAAQAKwAEAAQABAAEAAQAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwAEAAQABAAEAAQABAAEAFAAUABQAFAAUABQAFAAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwBQAB4AKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArABsAUABQAFAAUABQACsAKwBQAFAAUABQAFAAUABQAFAAUAAEAAQABAAEAAQABAAEACsAKwArACsAKwArACsAKwArAB4AHgAeAB4ABAAEAAQABAAEAAQABABQACsAKwArACsASwBLAEsASwBLAEsASwBLAEsASwArACsAKwArABYAFgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAGgBQAFAAUAAaAFAAUABQAFAAKwArACsAKwArACsAKwArACsAKwArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAeAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQACsAKwBQAFAAUABQACsAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwBQAFAAKwBQACsAKwBQACsAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAKwBQACsAUAArACsAKwArACsAKwBQACsAKwArACsAUAArAFAAKwBQACsAUABQAFAAKwBQAFAAKwBQACsAKwBQACsAUAArAFAAKwBQACsAUAArAFAAUAArAFAAKwArAFAAUABQAFAAKwBQAFAAUABQAFAAUABQACsAUABQAFAAUAArAFAAUABQAFAAKwBQACsAUABQAFAAUABQAFAAUABQAFAAUAArAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAArACsAKwArACsAUABQAFAAKwBQAFAAUABQAFAAKwBQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwAeAB4AKwArACsAKwArACsAKwArACsAKwArACsAKwArAE8ATwBPAE8ATwBPAE8ATwBPAE8ATwBPAE8AJQAlACUAHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHgAeAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB4AHgAeACUAJQAlAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAdAB0AHQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQApACkAKQApACkAKQApACkAKQApACkAKQApACkAKQApACkAKQApACkAKQApACkAKQApACkAJQAlACUAJQAlACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAeAB4AJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlAB4AHgAlACUAJQAlACUAHgAlACUAJQAlACUAIAAgACAAJQAlACAAJQAlACAAIAAgACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACEAIQAhACEAIQAlACUAIAAgACUAJQAgACAAIAAgACAAIAAgACAAIAAgACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAJQAlACUAIAAlACUAJQAlACAAIAAgACUAIAAgACAAJQAlACUAJQAlACUAJQAgACUAIAAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAHgAlAB4AJQAeACUAJQAlACUAJQAgACUAJQAlACUAHgAlAB4AHgAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlAB4AHgAeAB4AHgAeAB4AJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAeACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACAAIAAlACUAJQAlACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACAAJQAlACUAJQAgACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAHgAeAB4AHgAeAB4AHgAeACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAeAB4AHgAeAB4AHgAlACUAJQAlACUAJQAlACAAIAAgACUAJQAlACAAIAAgACAAIAAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeABcAFwAXABUAFQAVAB4AHgAeAB4AJQAlACUAIAAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACAAIAAgACUAJQAlACUAJQAlACUAJQAlACAAJQAlACUAJQAlACUAJQAlACUAJQAlACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AJQAlACUAJQAlACUAJQAlACUAJQAlACUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AJQAlACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeACUAJQAlACUAJQAlACUAJQAeAB4AHgAeAB4AHgAeAB4AHgAeACUAJQAlACUAJQAlAB4AHgAeAB4AHgAeAB4AHgAlACUAJQAlACUAJQAlACUAHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAgACUAJQAgACUAJQAlACUAJQAlACUAJQAgACAAIAAgACAAIAAgACAAJQAlACUAJQAlACUAIAAlACUAJQAlACUAJQAlACUAJQAgACAAIAAgACAAIAAgACAAIAAgACUAJQAgACAAIAAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAgACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACAAIAAlACAAIAAlACAAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAgACAAIAAlACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAJQAlAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AKwAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAEsASwBLAEsASwBLAEsASwBLAEsAKwArACsAKwArACsAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAKwArAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXACUAJQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwAlACUAJQAlACUAJQAlACUAJQAlACUAVwBXACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQBXAFcAVwBXAFcAVwBXAFcAVwBXAFcAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAJQAlACUAKwAEACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArACsAKwArAA=="),IA=Array.isArray(HA)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(HA):new Uint32Array(HA),xA=Array.isArray(HA)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(HA):new Uint16Array(HA),KA=CA(xA,12,IA[4]/2),LA=2===IA[5]?CA(xA,(24+IA[4])/2):(mA=IA,bA=Math.ceil((24+IA[4])/4),mA.slice?mA.slice(bA,EA):new Uint32Array(Array.prototype.slice.call(mA,bA,EA))),new UA(IA[0],IA[1],IA[2],IA[3],KA,LA)),ie=[YA,36],ae=[1,2,3,5],le=[DA,8],ce=[JA,GA],Be=ae.concat(le),ue=[qA,Ae,ee,jA,zA],de=[TA,SA],ge=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var s=t;s<=r.length;){if((a=r[++s])===e)return!0;if(a!==DA)break}if(n===DA)for(s=t;s>0;){var o=r[--s];if(Array.isArray(A)?-1!==A.indexOf(o):A===o)for(var i=t;i<=r.length;){var a;if((a=r[++i])===e)return!0;if(a!==DA)break}if(o!==DA)break}return!1},we=function(A,e){for(var t=A;t>=0;){var r=e[t];if(r!==DA)return r;t--}return 0},fe=function(A,e,t,r,n){if(0===t[r])return ne;var s=r-1;if(Array.isArray(n)&&!0===n[s])return ne;var o=s-1,i=s+1,a=e[s],l=o>=0?e[o]:0,c=e[i];if(2===a&&3===c)return ne;if(-1!==ae.indexOf(a))return"!";if(-1!==ae.indexOf(c))return ne;if(-1!==le.indexOf(c))return ne;if(8===we(s,e))return se;if(11===oe.get(A[s]))return ne;if((a===WA||a===ZA)&&11===oe.get(A[i]))return ne;if(7===a||7===c)return ne;if(9===a)return ne;if(-1===[DA,SA,TA].indexOf(a)&&9===c)return ne;if(-1!==[MA,OA,kA,VA,XA].indexOf(c))return ne;if(we(s,e)===PA)return ne;if(ge(23,PA,s,e))return ne;if(ge([MA,OA],RA,s,e))return ne;if(ge(12,12,s,e))return ne;if(a===DA)return se;if(23===a||23===c)return ne;if(16===c||16===a)return se;if(-1!==[SA,TA,RA].indexOf(c)||14===a)return ne;if(36===l&&-1!==de.indexOf(a))return ne;if(a===XA&&36===c)return ne;if(c===_A)return ne;if(-1!==ie.indexOf(c)&&a===NA||-1!==ie.indexOf(a)&&c===NA)return ne;if(a===JA&&-1!==[$A,WA,ZA].indexOf(c)||-1!==[$A,WA,ZA].indexOf(a)&&c===GA)return ne;if(-1!==ie.indexOf(a)&&-1!==ce.indexOf(c)||-1!==ce.indexOf(a)&&-1!==ie.indexOf(c))return ne;if(-1!==[JA,GA].indexOf(a)&&(c===NA||-1!==[PA,TA].indexOf(c)&&e[i+1]===NA)||-1!==[PA,TA].indexOf(a)&&c===NA||a===NA&&-1!==[NA,XA,VA].indexOf(c))return ne;if(-1!==[NA,XA,VA,MA,OA].indexOf(c))for(var B=s;B>=0;){if((u=e[B])===NA)return ne;if(-1===[XA,VA].indexOf(u))break;B--}if(-1!==[JA,GA].indexOf(c))for(B=-1!==[MA,OA].indexOf(a)?o:s;B>=0;){var u;if((u=e[B])===NA)return ne;if(-1===[XA,VA].indexOf(u))break;B--}if(qA===a&&-1!==[qA,Ae,jA,zA].indexOf(c)||-1!==[Ae,jA].indexOf(a)&&-1!==[Ae,ee].indexOf(c)||-1!==[ee,zA].indexOf(a)&&c===ee)return ne;if(-1!==ue.indexOf(a)&&-1!==[_A,GA].indexOf(c)||-1!==ue.indexOf(c)&&a===JA)return ne;if(-1!==ie.indexOf(a)&&-1!==ie.indexOf(c))return ne;if(a===VA&&-1!==ie.indexOf(c))return ne;if(-1!==ie.concat(NA).indexOf(a)&&c===PA&&-1===re.indexOf(A[i])||-1!==ie.concat(NA).indexOf(c)&&a===OA)return ne;if(41===a&&41===c){for(var d=t[s],g=1;d>0&&41===e[--d];)g++;if(g%2!=0)return ne}return a===WA&&c===ZA?ne:se},pe=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,s){var o=oe.get(A);if(o>50?(n.push(!0),o-=50):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(s),t.push(16);if(4===o||11===o){if(0===s)return r.push(s),t.push(YA);var i=t[s-1];return-1===Be.indexOf(i)?(r.push(r[s-1]),t.push(i)):(r.push(s),t.push(YA))}return r.push(s),31===o?t.push("strict"===e?RA:$A):o===te||29===o?t.push(YA):43===o?A>=131072&&A<=196605||A>=196608&&A<=262141?t.push($A):t.push(YA):void t.push(o)})),[r,t,n]}(A,e.lineBreak),r=t[0],n=t[1],s=t[2];"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[NA,YA,te].indexOf(A)?$A:A})));var o="keep-all"===e.wordBreak?s.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0;return[r,n,o]},Qe=function(){function A(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}return A.prototype.slice=function(){return dA.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),he=45,Ce=43,Ue=-1,Fe=function(A){return A>=48&&A<=57},ye=function(A){return Fe(A)||A>=65&&A<=70||A>=97&&A<=102},ve=function(A){return 10===A||9===A||32===A},me=function(A){return function(A){return function(A){return A>=97&&A<=122}(A)||function(A){return A>=65&&A<=90}(A)}(A)||function(A){return A>=128}(A)||95===A},be=function(A){return me(A)||Fe(A)||A===he},Ee=function(A){return A>=0&&A<=8||11===A||A>=14&&A<=31||127===A},He=function(A,e){return 92===A&&10!==e},Ie=function(A,e,t){return A===he?me(e)||He(e,t):!!me(A)||!(92!==A||!He(A,e))},xe=function(A,e,t){return A===Ce||A===he?!!Fe(e)||46===e&&Fe(t):Fe(46===A?e:A)},Ke=function(A){var e=0,t=1;A[e]!==Ce&&A[e]!==he||(A[e]===he&&(t=-1),e++);for(var r=[];Fe(A[e]);)r.push(A[e++]);var n=r.length?parseInt(dA.apply(void 0,r),10):0;46===A[e]&&e++;for(var s=[];Fe(A[e]);)s.push(A[e++]);var o=s.length,i=o?parseInt(dA.apply(void 0,s),10):0;69!==A[e]&&101!==A[e]||e++;var a=1;A[e]!==Ce&&A[e]!==he||(A[e]===he&&(a=-1),e++);for(var l=[];Fe(A[e]);)l.push(A[e++]);var c=l.length?parseInt(dA.apply(void 0,l),10):0;return t*(n+i*Math.pow(10,-o))*Math.pow(10,a*c)},Le={type:2},De={type:3},Se={type:4},Te={type:13},Me={type:8},Oe={type:21},ke={type:9},_e={type:10},Re={type:11},Pe={type:12},Ve={type:14},Ne={type:23},Ge={type:1},Je={type:25},Xe={type:24},Ye={type:26},We={type:27},Ze={type:28},je={type:29},ze={type:31},$e={type:32},qe=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(uA(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==$e;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(be(e)||He(t,r)){var n=Ie(e,t,r)?2:1;return{type:5,value:this.consumeName(),flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Te;break;case 39:return this.consumeStringToken(39);case 40:return Le;case 41:return De;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Ve;break;case Ce:if(xe(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return Se;case he:var s=A,o=this.peekCodePoint(0),i=this.peekCodePoint(1);if(xe(s,o,i))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(Ie(s,o,i))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(o===he&&62===i)return this.consumeCodePoint(),this.consumeCodePoint(),Xe;break;case 46:if(xe(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var a=this.consumeCodePoint();if(42===a&&47===(a=this.consumeCodePoint()))return this.consumeToken();if(a===Ue)return this.consumeToken()}break;case 58:return Ye;case 59:return We;case 60:if(33===this.peekCodePoint(0)&&this.peekCodePoint(1)===he&&this.peekCodePoint(2)===he)return this.consumeCodePoint(),this.consumeCodePoint(),Je;break;case 64:var l=this.peekCodePoint(0),c=this.peekCodePoint(1),B=this.peekCodePoint(2);if(Ie(l,c,B))return{type:7,value:this.consumeName()};break;case 91:return Ze;case 92:if(He(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return je;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Me;break;case 123:return Re;case 125:return Pe;case 117:case 85:var u=this.peekCodePoint(0),d=this.peekCodePoint(1);return u!==Ce||!ye(d)&&63!==d||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),ke;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),Oe;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),_e;break;case Ue:return $e}return ve(A)?(this.consumeWhiteSpace(),ze):Fe(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):me(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:dA(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();ye(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(dA.apply(void 0,A.map((function(A){return 63===A?48:A}))),16),end:parseInt(dA.apply(void 0,A.map((function(A){return 63===A?70:A}))),16)};var r=parseInt(dA.apply(void 0,A),16);if(this.peekCodePoint(0)===he&&ye(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var n=[];ye(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(dA.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===Ue)return{type:22,value:""};var e=this.peekCodePoint(0);if(39===e||34===e){var t=this.consumeStringToken(this.consumeCodePoint());return 0===t.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===Ue||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),Ne)}for(;;){var r=this.consumeCodePoint();if(r===Ue||41===r)return{type:22,value:dA.apply(void 0,A)};if(ve(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===Ue||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:22,value:dA.apply(void 0,A)}):(this.consumeBadUrlRemnants(),Ne);if(34===r||39===r||40===r||Ee(r))return this.consumeBadUrlRemnants(),Ne;if(92===r){if(!He(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),Ne;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;ve(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||A===Ue)return;He(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e="";A>0;){var t=Math.min(5e4,A);e+=dA.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r=this._value[t];if(r===Ue||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(10===r)return this._value.splice(0,t),Ge;if(92===r){var n=this._value[t+1];n!==Ue&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):He(r,n)&&(e+=this.consumeStringSlice(t),e+=dA(this.consumeEscapedCodePoint()),t=-1))}t++}},A.prototype.consumeNumber=function(){var A=[],e=4,t=this.peekCodePoint(0);for(t!==Ce&&t!==he||A.push(this.consumeCodePoint());Fe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(46===t&&Fe(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;Fe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((69===t||101===t)&&((r===Ce||r===he)&&Fe(n)||Fe(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;Fe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ke(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),s=this.peekCodePoint(2);return Ie(r,n,s)?{type:15,number:e,flags:t,unit:this.consumeName()}:37===r?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(ye(A)){for(var e=dA(A);ye(this.peekCodePoint(0))&&e.length<6;)e+=dA(this.consumeCodePoint());ve(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||function(A){return A>=55296&&A<=57343}(t)||t>1114111?65533:t}return A===Ue?65533:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(be(e))A+=dA(e);else{if(!He(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=dA(this.consumeEscapedCodePoint())}}},A}(),At=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new qe;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||lt(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?$e:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),et=function(A){return 15===A.type},tt=function(A){return 17===A.type},rt=function(A){return 20===A.type},nt=function(A){return 0===A.type},st=function(A,e){return rt(A)&&A.value===e},ot=function(A){return 31!==A.type},it=function(A){return 31!==A.type&&4!==A.type},at=function(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e},lt=function(A,e){return 11===e&&12===A.type||(28===e&&29===A.type||2===e&&3===A.type)},ct=function(A){return 17===A.type||15===A.type},Bt=function(A){return 16===A.type||ct(A)},ut=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},dt={type:17,number:0,flags:4},gt={type:16,number:50,flags:4},wt={type:16,number:100,flags:4},ft=function(A,e,t){var r=A[0],n=A[1];return[pt(r,e),pt(void 0!==n?n:r,t)]},pt=function(A,e){if(16===A.type)return A.number/100*e;if(et(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},Qt="grad",ht="turn",Ct=function(A,e){if(15===e.type)switch(e.unit){case"deg":return Math.PI*e.number/180;case Qt:return Math.PI/200*e.number;case"rad":return e.number;case ht:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},Ut=function(A){return 15===A.type&&("deg"===A.unit||A.unit===Qt||"rad"===A.unit||A.unit===ht)},Ft=function(A){switch(A.filter(rt).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[dt,dt];case"to top":case"bottom":return yt(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[dt,wt];case"to right":case"left":return yt(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[wt,wt];case"to bottom":case"top":return yt(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[wt,dt];case"to left":case"right":return yt(270)}return 0},yt=function(A){return Math.PI*A/180},vt=function(A,e){if(18===e.type){var t=Lt[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);return Et(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(4===e.value.length){r=e.value.substring(0,1),n=e.value.substring(1,2),s=e.value.substring(2,3);var o=e.value.substring(3,4);return Et(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(o+o,16)/255)}if(6===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6);return Et(parseInt(r,16),parseInt(n,16),parseInt(s,16),1)}if(8===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),s=e.value.substring(4,6),o=e.value.substring(6,8);return Et(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(o,16)/255)}}if(20===e.type){var i=St[e.value.toUpperCase()];if(void 0!==i)return i}return St.TRANSPARENT},mt=function(A){return!(255&A)},bt=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},Et=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r))>>>0},Ht=function(A,e){if(17===A.type)return A.number;if(16===A.type){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},It=function(A,e){var t=e.filter(it);if(3===t.length){var r=t.map(Ht),n=r[0],s=r[1],o=r[2];return Et(n,s,o,1)}if(4===t.length){var i=t.map(Ht),a=(n=i[0],s=i[1],o=i[2],i[3]);return Et(n,s,o,a)}return 0};function xt(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var Kt=function(A,e){var t=e.filter(it),r=t[0],n=t[1],s=t[2],o=t[3],i=(17===r.type?yt(r.number):Ct(A,r))/(2*Math.PI),a=Bt(n)?n.number/100:0,l=Bt(s)?s.number/100:0,c=void 0!==o&&Bt(o)?pt(o,1):1;if(0===a)return Et(255*l,255*l,255*l,1);var B=l<=.5?l*(a+1):l+a-l*a,u=2*l-B,d=xt(u,B,i+1/3),g=xt(u,B,i),w=xt(u,B,i-1/3);return Et(255*d,255*g,255*w,c)},Lt={hsl:Kt,hsla:Kt,rgb:It,rgba:It},Dt=function(A,e){return vt(A,At.create(e).parseComponentValue())},St={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},Tt={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(rt(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Mt={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ot=function(A,e){var t=vt(A,e[0]),r=e[1];return r&&Bt(r)?{color:t,stop:r}:{color:t,stop:null}},kt=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=dt),null===r.stop&&(r.stop=wt);for(var n=[],s=0,o=0;o<A.length;o++){var i=A[o].stop;if(null!==i){var a=pt(i,e);a>s?n.push(a):n.push(s),s=a}else n.push(null)}var l=null;for(o=0;o<n.length;o++){var c=n[o];if(null===c)null===l&&(l=o);else if(null!==l){for(var B=o-l,u=(c-n[l-1])/(B+1),d=1;d<=B;d++)n[l+d-1]=u*d;l=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,n[t]/e),0)}}))},_t=function(A,e,t){var r="number"==typeof A?A:function(A,e,t){var r=e/2,n=t/2,s=pt(A[0],e)-r,o=n-pt(A[1],t);return(Math.atan2(o,s)+2*Math.PI)%(2*Math.PI)}(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),s=e/2,o=t/2,i=n/2,a=Math.sin(r-Math.PI/2)*i,l=Math.cos(r-Math.PI/2)*i;return[n,s-l,s+l,o-a,o+a]},Rt=function(A,e){return Math.sqrt(A*A+e*e)},Pt=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var s=e[0],o=e[1],i=Rt(t-s,r-o);return(n?i<A.optimumDistance:i>A.optimumDistance)?{optimumCorner:e,optimumDistance:i}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Vt=function(A,e){var t=yt(180),r=[];return at(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&-1!==["top","left","right","bottom"].indexOf(s.value))return void(t=Ft(e));if(Ut(s))return void(t=(Ct(A,s)+yt(270))%yt(360))}var o=Ot(A,e);r.push(o)})),{angle:t,stops:r,type:1}},Nt="closest-side",Gt="farthest-side",Jt="closest-corner",Xt="farthest-corner",Yt="circle",Wt="ellipse",Zt="cover",jt="contain",zt=function(A,e){var t=0,r=3,n=[],s=[];return at(e).forEach((function(e,o){var i=!0;if(0===o?i=e.reduce((function(A,e){if(rt(e))switch(e.value){case"center":return s.push(gt),!1;case"top":case"left":return s.push(dt),!1;case"right":case"bottom":return s.push(wt),!1}else if(Bt(e)||ct(e))return s.push(e),!1;return A}),i):1===o&&(i=e.reduce((function(A,e){if(rt(e))switch(e.value){case Yt:return t=0,!1;case Wt:return t=1,!1;case jt:case Nt:return r=0,!1;case Gt:return r=1,!1;case Jt:return r=2,!1;case Zt:case Xt:return r=3,!1}else if(ct(e)||Bt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),i)),i){var a=Ot(A,e);n.push(a)}})),{size:r,shape:t,stops:n,position:s,type:2}},$t=function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18===e.type){var r=er[e.name];if(void 0===r)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return r(A,e.values)}throw new Error("Unsupported image type "+e.type)};var qt,Ar,er={"linear-gradient":function(A,e){var t=yt(180),r=[];return at(e).forEach((function(e,n){if(0===n){var s=e[0];if(20===s.type&&"to"===s.value)return void(t=Ft(e));if(Ut(s))return void(t=Ct(A,s))}var o=Ot(A,e);r.push(o)})),{angle:t,stops:r,type:1}},"-moz-linear-gradient":Vt,"-ms-linear-gradient":Vt,"-o-linear-gradient":Vt,"-webkit-linear-gradient":Vt,"radial-gradient":function(A,e){var t=0,r=3,n=[],s=[];return at(e).forEach((function(e,o){var i=!0;if(0===o){var a=!1;i=e.reduce((function(A,e){if(a)if(rt(e))switch(e.value){case"center":return s.push(gt),A;case"top":case"left":return s.push(dt),A;case"right":case"bottom":return s.push(wt),A}else(Bt(e)||ct(e))&&s.push(e);else if(rt(e))switch(e.value){case Yt:return t=0,!1;case Wt:return t=1,!1;case"at":return a=!0,!1;case Nt:return r=0,!1;case Zt:case Gt:return r=1,!1;case jt:case Jt:return r=2,!1;case Xt:return r=3,!1}else if(ct(e)||Bt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),i)}if(i){var l=Ot(A,e);n.push(l)}})),{size:r,shape:t,stops:n,position:s,type:2}},"-moz-radial-gradient":zt,"-ms-radial-gradient":zt,"-o-radial-gradient":zt,"-webkit-radial-gradient":zt,"-webkit-gradient":function(A,e){var t=yt(180),r=[],n=1;return at(e).forEach((function(e,t){var s=e[0];if(0===t){if(rt(s)&&"linear"===s.value)return void(n=1);if(rt(s)&&"radial"===s.value)return void(n=2)}if(18===s.type)if("from"===s.name){var o=vt(A,s.values[0]);r.push({stop:dt,color:o})}else if("to"===s.name){o=vt(A,s.values[0]);r.push({stop:wt,color:o})}else if("color-stop"===s.name){var i=s.values.filter(it);if(2===i.length){o=vt(A,i[1]);var a=i[0];tt(a)&&r.push({stop:{type:16,number:100*a.number,flags:a.flags},color:o})}}})),1===n?{angle:(t+yt(180))%yt(360),stops:r,type:n}:{size:3,shape:0,stops:r,position:[],type:n}}},tr={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return it(A)&&function(A){return!(20===A.type&&"none"===A.value||18===A.type&&!er[A.name])}(A)})).map((function(e){return $t(A,e)}))}},rr={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(rt(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},nr={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return at(e).map((function(A){return A.filter(Bt)})).map(ut)}},sr={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return at(e).map((function(A){return A.filter(rt).map((function(A){return A.value})).join(" ")})).map(or)}},or=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};(Ar=qt||(qt={})).AUTO="auto",Ar.CONTAIN="contain",Ar.COVER="cover";var ir,ar,lr={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return at(e).map((function(A){return A.filter(cr)}))}},cr=function(A){return rt(A)||Bt(A)},Br=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},ur=Br("top"),dr=Br("right"),gr=Br("bottom"),wr=Br("left"),fr=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return ut(e.filter(Bt))}}},pr=fr("top-left"),Qr=fr("top-right"),hr=fr("bottom-right"),Cr=fr("bottom-left"),Ur=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Fr=Ur("top"),yr=Ur("right"),vr=Ur("bottom"),mr=Ur("left"),br=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return et(e)?e.number:0}}},Er=br("top"),Hr=br("right"),Ir=br("bottom"),xr=br("left"),Kr={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Lr={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"===e?1:0}},Dr={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(rt).reduce((function(A,e){return A|Sr(e.value)}),0)}},Sr=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Tr={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Mr={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value?0:17===e.type||15===e.type?e.number:0}};(ar=ir||(ir={})).NORMAL="normal",ar.STRICT="strict";var Or,kr,_r={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"===e?ir.STRICT:ir.NORMAL}},Rr={name:"line-height",initialValue:"normal",prefix:!1,type:4},Pr=function(A,e){return rt(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:Bt(A)?pt(A,e):e},Vr={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:$t(A,e)}},Nr={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"===e?0:1}},Gr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},Jr=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},Xr=Jr("top"),Yr=Jr("right"),Wr=Jr("bottom"),Zr=Jr("left"),jr={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(rt).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},zr={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"===e?"break-word":"normal"}},$r=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},qr=$r("top"),An=$r("right"),en=$r("bottom"),tn=$r("left"),rn={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},nn={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},sn={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&st(e[0],"none")?[]:at(e).map((function(e){for(var t={color:St.TRANSPARENT,offsetX:dt,offsetY:dt,blur:dt},r=0,n=0;n<e.length;n++){var s=e[n];ct(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:t.blur=s,r++):t.color=vt(A,s)}return t}))}},on={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},an={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18===e.type){var t=ln[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}return null}},ln={matrix:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===e.length?e:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),t=e[0],r=e[1];e[2],e[3];var n=e[4],s=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var o=e[12],i=e[13];return e[14],e[15],16===e.length?[t,r,n,s,o,i]:null}},cn={type:16,number:50,flags:4},Bn=[cn,cn],un={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){var t=e.filter(Bt);return 2!==t.length?Bn:[t[0],t[1]]}},dn={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};(kr=Or||(Or={})).NORMAL="normal",kr.BREAK_ALL="break-all",kr.KEEP_ALL="keep-all";for(var gn={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return Or.BREAK_ALL;case"keep-all":return Or.KEEP_ALL;default:return Or.NORMAL}}},wn={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(tt(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},fn=function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")},pn={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return tt(e)?e.number:1}},Qn={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},hn={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(rt).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},Cn={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}})),t.length&&r.push(t.join(" ")),r.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},Un={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Fn={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return tt(e)?e.number:rt(e)&&"bold"===e.value?700:400}},yn={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(rt).map((function(A){return A.value}))}},vn={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},mn=function(A,e){return!!(A&e)},bn={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},En={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(ot),s=0;s<n.length;s++){var o=n[s],i=n[s+1];if(20===o.type){var a=i&&tt(i)?i.number:1;r.push({counter:o.value,increment:a})}}return r}},Hn={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(ot),n=0;n<r.length;n++){var s=r[n],o=r[n+1];if(rt(s)&&"none"!==s.value){var i=o&&tt(o)?o.number:0;t.push({counter:s.value,reset:i})}}return t}},In={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter(et).map((function(e){return fn(A,e)}))}},xn={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(nt);if(n.length%2!=0)return null;for(var s=0;s<n.length;s+=2){var o=n[s].value,i=n[s+1].value;r.push({open:o,close:i})}return r}},Kn=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},Ln={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&st(e[0],"none")?[]:at(e).map((function(e){for(var t={color:255,offsetX:dt,offsetY:dt,blur:dt,spread:dt,inset:!1},r=0,n=0;n<e.length;n++){var s=e[n];st(s,"inset")?t.inset=!0:ct(s)?(0===r?t.offsetX=s:1===r?t.offsetY=s:2===r?t.blur=s:t.spread=s,r++):t.color=vt(A,s)}return t}))}},Dn={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(rt).forEach((function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}})),[0,1,2].forEach((function(A){-1===t.indexOf(A)&&t.push(A)})),t}},Sn={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Tn={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return et(e)?e.number:0}},Mn=function(){function A(A,e){var t,r;this.animationDuration=_n(A,In,e.animationDuration),this.backgroundClip=_n(A,Tt,e.backgroundClip),this.backgroundColor=_n(A,Mt,e.backgroundColor),this.backgroundImage=_n(A,tr,e.backgroundImage),this.backgroundOrigin=_n(A,rr,e.backgroundOrigin),this.backgroundPosition=_n(A,nr,e.backgroundPosition),this.backgroundRepeat=_n(A,sr,e.backgroundRepeat),this.backgroundSize=_n(A,lr,e.backgroundSize),this.borderTopColor=_n(A,ur,e.borderTopColor),this.borderRightColor=_n(A,dr,e.borderRightColor),this.borderBottomColor=_n(A,gr,e.borderBottomColor),this.borderLeftColor=_n(A,wr,e.borderLeftColor),this.borderTopLeftRadius=_n(A,pr,e.borderTopLeftRadius),this.borderTopRightRadius=_n(A,Qr,e.borderTopRightRadius),this.borderBottomRightRadius=_n(A,hr,e.borderBottomRightRadius),this.borderBottomLeftRadius=_n(A,Cr,e.borderBottomLeftRadius),this.borderTopStyle=_n(A,Fr,e.borderTopStyle),this.borderRightStyle=_n(A,yr,e.borderRightStyle),this.borderBottomStyle=_n(A,vr,e.borderBottomStyle),this.borderLeftStyle=_n(A,mr,e.borderLeftStyle),this.borderTopWidth=_n(A,Er,e.borderTopWidth),this.borderRightWidth=_n(A,Hr,e.borderRightWidth),this.borderBottomWidth=_n(A,Ir,e.borderBottomWidth),this.borderLeftWidth=_n(A,xr,e.borderLeftWidth),this.boxShadow=_n(A,Ln,e.boxShadow),this.color=_n(A,Kr,e.color),this.direction=_n(A,Lr,e.direction),this.display=_n(A,Dr,e.display),this.float=_n(A,Tr,e.cssFloat),this.fontFamily=_n(A,Cn,e.fontFamily),this.fontSize=_n(A,Un,e.fontSize),this.fontStyle=_n(A,vn,e.fontStyle),this.fontVariant=_n(A,yn,e.fontVariant),this.fontWeight=_n(A,Fn,e.fontWeight),this.letterSpacing=_n(A,Mr,e.letterSpacing),this.lineBreak=_n(A,_r,e.lineBreak),this.lineHeight=_n(A,Rr,e.lineHeight),this.listStyleImage=_n(A,Vr,e.listStyleImage),this.listStylePosition=_n(A,Nr,e.listStylePosition),this.listStyleType=_n(A,Gr,e.listStyleType),this.marginTop=_n(A,Xr,e.marginTop),this.marginRight=_n(A,Yr,e.marginRight),this.marginBottom=_n(A,Wr,e.marginBottom),this.marginLeft=_n(A,Zr,e.marginLeft),this.opacity=_n(A,pn,e.opacity);var n=_n(A,jr,e.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=_n(A,zr,e.overflowWrap),this.paddingTop=_n(A,qr,e.paddingTop),this.paddingRight=_n(A,An,e.paddingRight),this.paddingBottom=_n(A,en,e.paddingBottom),this.paddingLeft=_n(A,tn,e.paddingLeft),this.paintOrder=_n(A,Dn,e.paintOrder),this.position=_n(A,nn,e.position),this.textAlign=_n(A,rn,e.textAlign),this.textDecorationColor=_n(A,Qn,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=_n(A,hn,null!==(r=e.textDecorationLine)&&void 0!==r?r:e.textDecoration),this.textShadow=_n(A,sn,e.textShadow),this.textTransform=_n(A,on,e.textTransform),this.transform=_n(A,an,e.transform),this.transformOrigin=_n(A,un,e.transformOrigin),this.visibility=_n(A,dn,e.visibility),this.webkitTextStrokeColor=_n(A,Sn,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=_n(A,Tn,e.webkitTextStrokeWidth),this.wordBreak=_n(A,gn,e.wordBreak),this.zIndex=_n(A,wn,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return mt(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return mn(this.display,4)||mn(this.display,33554432)||mn(this.display,268435456)||mn(this.display,536870912)||mn(this.display,67108864)||mn(this.display,134217728)},A}(),On=function(){return function(A,e){this.content=_n(A,bn,e.content),this.quotes=_n(A,xn,e.quotes)}}(),kn=function(){return function(A,e){this.counterIncrement=_n(A,En,e.counterIncrement),this.counterReset=_n(A,Hn,e.counterReset)}}(),_n=function(A,e,t){var r=new qe,n=null!=t?t.toString():e.initialValue;r.write(n);var s=new At(r.read());switch(e.type){case 2:var o=s.parseComponentValue();return e.parse(A,rt(o)?o.value:e.initialValue);case 0:return e.parse(A,s.parseComponentValue());case 1:return e.parse(A,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(e.format){case"angle":return Ct(A,s.parseComponentValue());case"color":return vt(A,s.parseComponentValue());case"image":return $t(A,s.parseComponentValue());case"length":var i=s.parseComponentValue();return ct(i)?i:dt;case"length-percentage":var a=s.parseComponentValue();return Bt(a)?a:dt;case"time":return fn(A,s.parseComponentValue())}}},Rn=function(A,e){var t=function(A){switch(A.getAttribute("data-html2canvas-debug")){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}}(A);return 1===t||e===t},Pn=function(){return function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Rn(e,3),this.styles=new Mn(A,window.getComputedStyle(e,null)),Xs(e)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=BA(this.context,e),Rn(e,4)&&(this.flags|=16)}}(),Vn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Nn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Gn=0;Gn<64;Gn++)Nn[Vn.charCodeAt(Gn)]=Gn;for(var Jn=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},Xn=function(){function A(A,e,t,r,n,s){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=s}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>5])<<2)+(31&A),this.data[e];if(A<=65535)return e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A),this.data[e];if(A<this.highStart)return e=2080+(A>>11),e=this.index[e],e+=A>>5&63,e=((e=this.index[e])<<2)+(31&A),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),Yn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Wn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Zn=0;Zn<64;Zn++)Wn[Yn.charCodeAt(Zn)]=Zn;var jn,zn,$n=8,qn=9,As=11,es=12,ts=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var o=A[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},rs=function(A){var e=function(A){var e,t,r,n,s,o=.75*A.length,i=A.length,a=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--);var l="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(o):new Array(o),c=Array.isArray(l)?l:new Uint8Array(l);for(e=0;e<i;e+=4)t=Nn[A.charCodeAt(e)],r=Nn[A.charCodeAt(e+1)],n=Nn[A.charCodeAt(e+2)],s=Nn[A.charCodeAt(e+3)],c[a++]=t<<2|r>>4,c[a++]=(15&r)<<4|n>>2,c[a++]=(3&n)<<6|63&s;return l}(A),t=Array.isArray(e)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(e):new Uint32Array(e),r=Array.isArray(e)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(e):new Uint16Array(e),n=Jn(r,12,t[4]/2),s=2===t[5]?Jn(r,(24+t[4])/2):function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))}(t,Math.ceil((24+t[4])/4));return new Xn(t[0],t[1],t[2],t[3],n,s)}("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"),ns="×",ss=function(A){return rs.get(A)},os=function(A,e,t){var r=t-2,n=e[r],s=e[t-1],o=e[t];if(2===s&&3===o)return ns;if(2===s||3===s||4===s)return"÷";if(2===o||3===o||4===o)return"÷";if(s===$n&&-1!==[$n,qn,As,es].indexOf(o))return ns;if(!(s!==As&&s!==qn||o!==qn&&10!==o))return ns;if((s===es||10===s)&&10===o)return ns;if(13===o||5===o)return ns;if(7===o)return ns;if(1===s)return ns;if(13===s&&14===o){for(;5===n;)n=e[--r];if(14===n)return ns}if(15===s&&15===o){for(var i=0;15===n;)i++,n=e[--r];if(i%2==0)return ns}return"÷"},is=function(A){var e=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=A.charCodeAt(t++);56320==(64512&s)?e.push(((1023&n)<<10)+(1023&s)+65536):(e.push(n),t--)}else e.push(n)}return e}(A),t=e.length,r=0,n=0,s=e.map(ss);return{next:function(){if(r>=t)return{done:!0,value:null};for(var A=ns;r<t&&(A=os(0,s,++r))===ns;);if(A!==ns||r===t){var o=ts.apply(null,e.slice(n,r));return n=r,{value:o,done:!1}}return{done:!0,value:null}}}},as=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},ls=function(A,e,t,r,n){var s="http://www.w3.org/2000/svg",o=document.createElementNS(s,"svg"),i=document.createElementNS(s,"foreignObject");return o.setAttributeNS(null,"width",A.toString()),o.setAttributeNS(null,"height",e.toString()),i.setAttributeNS(null,"width","100%"),i.setAttributeNS(null,"height","100%"),i.setAttributeNS(null,"x",t.toString()),i.setAttributeNS(null,"y",r.toString()),i.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(i),i.appendChild(n),o},cs=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Bs={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(A.body.removeChild(t),123===n)return!0}}return!1}(document);return Object.defineProperty(Bs,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Bs.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var r=e.firstChild,n=uA(r.data).map((function(A){return dA(A)})),s=0,o={},i=n.every((function(A,e){t.setStart(r,s),t.setEnd(r,s+A.length);var n=t.getBoundingClientRect();s+=A.length;var i=n.x>o.x||n.y>o.y;return o=n,0===e||i}));return A.body.removeChild(e),i}(document);return Object.defineProperty(Bs,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(n){return!1}return!0}(document);return Object.defineProperty(Bs,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=e.toDataURL();n.src=s;var o=ls(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),cs(o).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var o=A.createElement("div");return o.style.backgroundImage="url("+s+")",o.style.height=t+"px",as(n)?cs(ls(t,t,0,0,o)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),as(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))}(document):Promise.resolve(!1);return Object.defineProperty(Bs,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Bs,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Bs,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Bs,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(Bs,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},us=function(){return function(A,e){this.text=A,this.bounds=e}}(),ds=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(r,e);var s=BA(A,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return cA.EMPTY},gs=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),n},ws=function(A){if(Bs.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return function(A){for(var e,t=is(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r}(A)},fs=function(A,e){return 0!==e.letterSpacing?ws(A):function(A,e){if(Bs.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return Qs(A,e)}(A,e)},ps=[32,160,4961,65792,65793,4153,4241],Qs=function(A,e){for(var t,r=function(A,e){var t=uA(A),r=pe(t,e),n=r[0],s=r[1],o=r[2],i=t.length,a=0,l=0;return{next:function(){if(l>=i)return{done:!0,value:null};for(var A=ne;l<i&&(A=fe(t,s,n,++l,o))===ne;);if(A!==ne||l===i){var e=new Qe(t,A,a,l);return a=l,{value:e,done:!1}}return{done:!0,value:null}}}}(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[],s=function(){if(t.value){var A=t.value.slice(),e=uA(A),r="";e.forEach((function(A){-1===ps.indexOf(A)?r+=dA(A):(r.length&&n.push(r),n.push(dA(A)),r="")})),r.length&&n.push(r)}};!(t=r.next()).done;)s();return n},hs=function(){return function(A,e,t){this.text=Cs(e.data,t.textTransform),this.textBounds=function(A,e,t,r){var n=fs(e,t),s=[],o=0;return n.forEach((function(e){if(t.textDecorationLine.length||e.trim().length>0)if(Bs.SUPPORT_RANGE_BOUNDS){var n=gs(r,o,e.length).getClientRects();if(n.length>1){var i=ws(e),a=0;i.forEach((function(e){s.push(new us(e,cA.fromDOMRectList(A,gs(r,a+o,e.length).getClientRects()))),a+=e.length}))}else s.push(new us(e,cA.fromDOMRectList(A,n)))}else{var l=r.splitText(e.length);s.push(new us(e,ds(A,r))),r=l}else Bs.SUPPORT_RANGE_BOUNDS||(r=r.splitText(e.length));o+=e.length})),s}(A,this.text,t,e)}}(),Cs=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(Us,Fs);case 2:return A.toUpperCase();default:return A}},Us=/(^|\s|:|-|\(|\))([a-z])/g,Fs=function(A,e,t){return A.length>0?e+t.toUpperCase():A},ys=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return sA(e,A),e}(Pn),vs=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return sA(e,A),e}(Pn),ms=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=new XMLSerializer,s=BA(e,t);return t.setAttribute("width",s.width+"px"),t.setAttribute("height",s.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return sA(e,A),e}(Pn),bs=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return sA(e,A),e}(Pn),Es=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.start=t.start,r.reversed="boolean"==typeof t.reversed&&!0===t.reversed,r}return sA(e,A),e}(Pn),Hs=[{type:15,flags:0,unit:"px",number:3}],Is=[{type:16,flags:0,number:50}],xs="checkbox",Ks="radio",Ls="password",Ds=707406591,Ss=function(A){function e(e,t){var r,n,s,o=A.call(this,e,t)||this;switch(o.type=t.type.toLowerCase(),o.checked=t.checked,o.value=0===(n=(r=t).type===Ls?new Array(r.value.length+1).join("•"):r.value).length?r.placeholder||"":n,o.type!==xs&&o.type!==Ks||(o.styles.backgroundColor=3739148031,o.styles.borderTopColor=o.styles.borderRightColor=o.styles.borderBottomColor=o.styles.borderLeftColor=2779096575,o.styles.borderTopWidth=o.styles.borderRightWidth=o.styles.borderBottomWidth=o.styles.borderLeftWidth=1,o.styles.borderTopStyle=o.styles.borderRightStyle=o.styles.borderBottomStyle=o.styles.borderLeftStyle=1,o.styles.backgroundClip=[0],o.styles.backgroundOrigin=[0],o.bounds=(s=o.bounds).width>s.height?new cA(s.left+(s.width-s.height)/2,s.top,s.height,s.height):s.width<s.height?new cA(s.left,s.top+(s.height-s.width)/2,s.width,s.width):s),o.type){case xs:o.styles.borderTopRightRadius=o.styles.borderTopLeftRadius=o.styles.borderBottomRightRadius=o.styles.borderBottomLeftRadius=Hs;break;case Ks:o.styles.borderTopRightRadius=o.styles.borderTopLeftRadius=o.styles.borderBottomRightRadius=o.styles.borderBottomLeftRadius=Is}return o}return sA(e,A),e}(Pn),Ts=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return sA(e,A),e}(Pn),Ms=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return sA(e,A),e}(Pn),Os=function(A){function e(e,t){var r=A.call(this,e,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=Ps(e,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?Dt(e,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):St.TRANSPARENT,s=t.contentWindow.document.body?Dt(e,getComputedStyle(t.contentWindow.document.body).backgroundColor):St.TRANSPARENT;r.backgroundColor=mt(n)?mt(s)?r.styles.backgroundColor:s:n}}catch(o){}return r}return sA(e,A),e}(Pn),ks=["OL","UL","MENU"],_s=function(A,e,t,r){for(var n=e.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,Gs(n)&&n.data.trim().length>0)t.textNodes.push(new hs(A,n,t.styles));else if(Js(n))if(oo(n)&&n.assignedNodes)n.assignedNodes().forEach((function(e){return _s(A,e,t,r)}));else{var o=Rs(A,n);o.styles.isVisible()&&(Vs(n,o,r)?o.flags|=4:Ns(o.styles)&&(o.flags|=2),-1!==ks.indexOf(n.tagName)&&(o.flags|=8),t.elements.push(o),n.slot,n.shadowRoot?_s(A,n.shadowRoot,o,r):no(n)||zs(n)||so(n)||_s(A,n,o,r))}},Rs=function(A,e){return eo(e)?new ys(A,e):qs(e)?new vs(A,e):zs(e)?new ms(A,e):Ws(e)?new bs(A,e):Zs(e)?new Es(A,e):js(e)?new Ss(A,e):so(e)?new Ts(A,e):no(e)?new Ms(A,e):to(e)?new Os(A,e):new Pn(A,e)},Ps=function(A,e){var t=Rs(A,e);return t.flags|=4,_s(A,e,t,t),t},Vs=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||$s(A)&&t.styles.isTransparent()},Ns=function(A){return A.isPositioned()||A.isFloating()},Gs=function(A){return A.nodeType===Node.TEXT_NODE},Js=function(A){return A.nodeType===Node.ELEMENT_NODE},Xs=function(A){return Js(A)&&void 0!==A.style&&!Ys(A)},Ys=function(A){return"object"==typeof A.className},Ws=function(A){return"LI"===A.tagName},Zs=function(A){return"OL"===A.tagName},js=function(A){return"INPUT"===A.tagName},zs=function(A){return"svg"===A.tagName},$s=function(A){return"BODY"===A.tagName},qs=function(A){return"CANVAS"===A.tagName},Ao=function(A){return"VIDEO"===A.tagName},eo=function(A){return"IMG"===A.tagName},to=function(A){return"IFRAME"===A.tagName},ro=function(A){return"STYLE"===A.tagName},no=function(A){return"TEXTAREA"===A.tagName},so=function(A){return"SELECT"===A.tagName},oo=function(A){return"SLOT"===A.tagName},io=function(A){return A.tagName.indexOf("-")>0},ao=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset,n=!0;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var s=[];return n&&r.forEach((function(A){var t=e.counters[A.counter];s.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),s},A}(),lo={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},co={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},Bo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},uo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},go=function(A,e,t,r,n,s){return A<e||A>t?yo(A,n,s.length>0):r.integers.reduce((function(e,t,n){for(;A>=t;)A-=t,e+=r.values[n];return e}),"")+s},wo=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},fo=function(A,e,t,r,n){var s=t-e+1;return(A<0?"-":"")+(wo(Math.abs(A),s,r,(function(A){return dA(Math.floor(A%s)+e)}))+n)},po=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return wo(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},Qo=function(A,e,t,r,n,s){if(A<-9999||A>9999)return yo(A,4,n.length>0);var o=Math.abs(A),i=n;if(0===o)return e[0]+i;for(var a=0;o>0&&a<=4;a++){var l=o%10;0===l&&mn(s,1)&&""!==i?i=e[l]+i:l>1||1===l&&0===a||1===l&&1===a&&mn(s,2)||1===l&&1===a&&mn(s,4)&&A>100||1===l&&a>1&&mn(s,8)?i=e[l]+(a>0?t[a-1]:"")+i:1===l&&a>0&&(i=t[a-1]+i),o=Math.floor(o/10)}return(A<0?r:"")+i},ho="十百千萬",Co="拾佰仟萬",Uo="マイナス",Fo="마이너스",yo=function(A,e,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",o=t?" ":"";switch(e){case 0:return"•"+o;case 1:return"◦"+o;case 2:return"◾"+o;case 5:var i=fo(A,48,57,!0,r);return i.length<4?"0"+i:i;case 4:return po(A,"〇一二三四五六七八九",n);case 6:return go(A,1,3999,lo,3,r).toLowerCase();case 7:return go(A,1,3999,lo,3,r);case 8:return fo(A,945,969,!1,r);case 9:return fo(A,97,122,!1,r);case 10:return fo(A,65,90,!1,r);case 11:return fo(A,1632,1641,!0,r);case 12:case 49:return go(A,1,9999,co,3,r);case 35:return go(A,1,9999,co,3,r).toLowerCase();case 13:return fo(A,2534,2543,!0,r);case 14:case 30:return fo(A,6112,6121,!0,r);case 15:return po(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return po(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return Qo(A,"零一二三四五六七八九",ho,"負",n,14);case 47:return Qo(A,"零壹貳參肆伍陸柒捌玖",Co,"負",n,15);case 42:return Qo(A,"零一二三四五六七八九",ho,"负",n,14);case 41:return Qo(A,"零壹贰叁肆伍陆柒捌玖",Co,"负",n,15);case 26:return Qo(A,"〇一二三四五六七八九","十百千万",Uo,n,0);case 25:return Qo(A,"零壱弐参四伍六七八九","拾百千万",Uo,n,7);case 31:return Qo(A,"영일이삼사오육칠팔구","십백천만",Fo,s,7);case 33:return Qo(A,"零一二三四五六七八九","十百千萬",Fo,s,0);case 32:return Qo(A,"零壹貳參四五六七八九","拾百千",Fo,s,7);case 18:return fo(A,2406,2415,!0,r);case 20:return go(A,1,19999,uo,3,r);case 21:return fo(A,2790,2799,!0,r);case 22:return fo(A,2662,2671,!0,r);case 22:return go(A,1,10999,Bo,3,r);case 23:return po(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return po(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return fo(A,3302,3311,!0,r);case 28:return po(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return po(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return fo(A,3792,3801,!0,r);case 37:return fo(A,6160,6169,!0,r);case 38:return fo(A,4160,4169,!0,r);case 39:return fo(A,2918,2927,!0,r);case 40:return fo(A,1776,1785,!0,r);case 43:return fo(A,3046,3055,!0,r);case 44:return fo(A,3174,3183,!0,r);case 45:return fo(A,3664,3673,!0,r);case 46:return fo(A,3872,3881,!0,r);default:return fo(A,48,57,!0,r)}},vo="data-html2canvas-ignore",mo=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new ao,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,e){var t=this,r=Ho(A,e);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var n=A.defaultView.pageXOffset,s=A.defaultView.pageYOffset,o=r.contentWindow,i=o.document,a=Ko(r).then((function(){return iA(t,0,void 0,(function(){var A,t;return aA(this,(function(n){switch(n.label){case 0:return this.scrolledElements.forEach(Mo),o&&(o.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||o.scrollY===e.top&&o.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-e.left,o.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:n.sent(),n.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,xo(i)]:[3,4];case 3:n.sent(),n.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(i,t)})).then((function(){return r}))]:[2,r]}}))}))}));return i.open(),i.write(So(document.doctype)+"<html></html>"),To(this.referenceElement.ownerDocument,n,s),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),a},A.prototype.createElementClone=function(A){if(Rn(A,2),qs(A))return this.createCanvasClone(A);if(Ao(A))return this.createVideoClone(A);if(ro(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return eo(e)&&(eo(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),io(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return Do(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(n){if(this.context.logger.error("Unable to access cssRules property",n),"SecurityError"!==n.name)throw n}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(a){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var r=A.cloneNode(!1);try{r.width=A.width,r.height=A.height;var n=A.getContext("2d"),s=r.getContext("2d");if(s)if(!this.options.allowTaint&&n)s.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var o=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl");if(o){var i=o.getContextAttributes();!1===(null==i?void 0:i.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}s.drawImage(A,0,0)}return r}catch(a){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return r},A.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(n){this.context.logger.info("Unable to clone video as it is tainted",A)}var r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},A.prototype.appendChildNode=function(A,e,t){Js(e)&&("SCRIPT"===e.tagName||e.hasAttribute(vo)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&Js(e)&&ro(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(Js(n)&&oo(n)&&"function"==typeof n.assignedNodes){var s=n.assignedNodes();s.length&&s.forEach((function(A){return r.appendChildNode(e,A,t)}))}else this.appendChildNode(e,n,t)},A.prototype.cloneNode=function(A,e){if(Gs(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&Js(A)&&(Xs(A)||Ys(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),s=t.getComputedStyle(A,":before"),o=t.getComputedStyle(A,":after");this.referenceElement===A&&Xs(r)&&(this.clonedReferenceElement=r),$s(r)&&Ro(r);var i=this.counters.parse(new kn(this.context,n)),a=this.resolvePseudoContent(A,r,s,jn.BEFORE);io(A)&&(e=!0),Ao(A)||this.cloneChildNodes(A,r,e),a&&r.insertBefore(a,r.firstChild);var l=this.resolvePseudoContent(A,r,o,jn.AFTER);return l&&r.appendChild(l),this.counters.pop(i),(n&&(this.options.copyStyles||Ys(A))&&!to(A)||e)&&Do(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(no(A)||so(A))&&(no(r)||so(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var s=t.content,o=e.ownerDocument;if(o&&s&&"none"!==s&&"-moz-alt-content"!==s&&"none"!==t.display){this.counters.parse(new kn(this.context,t));var i=new On(this.context,t),a=o.createElement("html2canvaspseudoelement");Do(t,a),i.content.forEach((function(e){if(0===e.type)a.appendChild(o.createTextNode(e.value));else if(22===e.type){var t=o.createElement("img");t.src=e.value,t.style.opacity="1",a.appendChild(t)}else if(18===e.type){if("attr"===e.name){var r=e.values.filter(rt);r.length&&a.appendChild(o.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var s=e.values.filter(it),l=s[0],c=s[1];if(l&&rt(l)){var B=n.counters.getCounterValue(l.value),u=c&&rt(c)?Gr.parse(n.context,c.value):3;a.appendChild(o.createTextNode(yo(B,u,!1)))}}else if("counters"===e.name){var d=e.values.filter(it),g=(l=d[0],d[1]);c=d[2];if(l&&rt(l)){var w=n.counters.getCounterValues(l.value),f=c&&rt(c)?Gr.parse(n.context,c.value):3,p=g&&0===g.type?g.value:"",Q=w.map((function(A){return yo(A,f,!1)})).join(p);a.appendChild(o.createTextNode(Q))}}}else if(20===e.type)switch(e.value){case"open-quote":a.appendChild(o.createTextNode(Kn(i.quotes,n.quoteDepth++,!0)));break;case"close-quote":a.appendChild(o.createTextNode(Kn(i.quotes,--n.quoteDepth,!1)));break;default:a.appendChild(o.createTextNode(e.value))}})),a.className=Oo+" "+ko;var l=r===jn.BEFORE?" "+Oo:" "+ko;return Ys(e)?e.className.baseValue+=l:e.className+=l,a}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();(zn=jn||(jn={}))[zn.BEFORE=0]="BEFORE",zn[zn.AFTER=1]="AFTER";var bo,Eo,Ho=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(vo,"true"),A.body.appendChild(t),t},Io=function(A){return new Promise((function(e){A.complete?e():A.src?(A.onload=e,A.onerror=e):e()}))},xo=function(A){return Promise.all([].slice.call(A.images,0).map(Io))},Ko=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=function(){r.onload=A.onload=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},Lo=["all","d","content"],Do=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);-1===Lo.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},So=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},To=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},Mo=function(A){var e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},Oo="___html2canvas___pseudoelement_before",ko="___html2canvas___pseudoelement_after",_o='{\n    content: "" !important;\n    display: none !important;\n}',Ro=function(A){Po(A,"."+Oo+":before"+_o+"\n         ."+ko+":after"+_o)},Po=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}},Vo=function(){function A(){}return A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A._origin="about:blank",A}(),No=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:jo(A)||Yo(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return iA(this,0,void 0,(function(){var e,t,r,n,s=this;return aA(this,(function(o){switch(o.label){case 0:return e=Vo.isSameOrigin(A),t=!Wo(A)&&!0===this._options.useCORS&&Bs.SUPPORT_CORS_IMAGES&&!e,r=!Wo(A)&&!e&&!jo(A)&&"string"==typeof this._options.proxy&&Bs.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||Wo(A)||jo(A)||r||t?(n=A,r?[4,this.proxy(n)]:[3,2]):[2];case 1:n=o.sent(),o.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(Zo(n)||t)&&(r.crossOrigin="anonymous"),r.src=n,!0===r.complete&&setTimeout((function(){return A(r)}),500),s._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+s._options.imageTimeout+"ms) loading image")}),s._options.imageTimeout)}))];case 3:return[2,o.sent()]}}))}))},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,s){var o=Bs.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;i.onload=function(){if(200===i.status)if("text"===o)n(i.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return s(A)}),!1),A.readAsDataURL(i.response)}else s("Failed to proxy resource "+r+" with status code "+i.status)},i.onerror=s;var a=t.indexOf("?")>-1?"&":"?";if(i.open("GET",""+t+a+"url="+encodeURIComponent(A)+"&responseType="+o),"text"!==o&&i instanceof XMLHttpRequest&&(i.responseType=o),e._options.imageTimeout){var l=e._options.imageTimeout;i.timeout=l,i.ontimeout=function(){return s("Timed out ("+l+"ms) proxying "+r)}}i.send()}))},A}(),Go=/^data:image\/svg\+xml/i,Jo=/^data:image\/.*;base64,/i,Xo=/^data:image\/.*/i,Yo=function(A){return Bs.SUPPORT_SVG_DRAWING||!zo(A)},Wo=function(A){return Xo.test(A)},Zo=function(A){return Jo.test(A)},jo=function(A){return"blob"===A.substr(0,4)},zo=function(A){return"svg"===A.substr(-3).toLowerCase()||Go.test(A)},$o=function(){function A(A,e){this.type=0,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),qo=function(A,e,t){return new $o(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},Ai=function(){function A(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=qo(this.start,this.startControl,e),n=qo(this.startControl,this.endControl,e),s=qo(this.endControl,this.end,e),o=qo(r,n,e),i=qo(n,s,e),a=qo(o,i,e);return t?new A(this.start,r,o,a):new A(a,i,s,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),ei=function(A){return 1===A.type},ti=function(){return function(A){var e=A.styles,t=A.bounds,r=ft(e.borderTopLeftRadius,t.width,t.height),n=r[0],s=r[1],o=ft(e.borderTopRightRadius,t.width,t.height),i=o[0],a=o[1],l=ft(e.borderBottomRightRadius,t.width,t.height),c=l[0],B=l[1],u=ft(e.borderBottomLeftRadius,t.width,t.height),d=u[0],g=u[1],w=[];w.push((n+i)/t.width),w.push((d+c)/t.width),w.push((s+g)/t.height),w.push((a+B)/t.height);var f=Math.max.apply(Math,w);f>1&&(n/=f,s/=f,i/=f,a/=f,c/=f,B/=f,d/=f,g/=f);var p=t.width-i,Q=t.height-B,h=t.width-c,C=t.height-g,U=e.borderTopWidth,F=e.borderRightWidth,y=e.borderBottomWidth,v=e.borderLeftWidth,m=pt(e.paddingTop,A.bounds.width),b=pt(e.paddingRight,A.bounds.width),E=pt(e.paddingBottom,A.bounds.width),H=pt(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||s>0?ri(t.left+v/3,t.top+U/3,n-v/3,s-U/3,bo.TOP_LEFT):new $o(t.left+v/3,t.top+U/3),this.topRightBorderDoubleOuterBox=n>0||s>0?ri(t.left+p,t.top+U/3,i-F/3,a-U/3,bo.TOP_RIGHT):new $o(t.left+t.width-F/3,t.top+U/3),this.bottomRightBorderDoubleOuterBox=c>0||B>0?ri(t.left+h,t.top+Q,c-F/3,B-y/3,bo.BOTTOM_RIGHT):new $o(t.left+t.width-F/3,t.top+t.height-y/3),this.bottomLeftBorderDoubleOuterBox=d>0||g>0?ri(t.left+v/3,t.top+C,d-v/3,g-y/3,bo.BOTTOM_LEFT):new $o(t.left+v/3,t.top+t.height-y/3),this.topLeftBorderDoubleInnerBox=n>0||s>0?ri(t.left+2*v/3,t.top+2*U/3,n-2*v/3,s-2*U/3,bo.TOP_LEFT):new $o(t.left+2*v/3,t.top+2*U/3),this.topRightBorderDoubleInnerBox=n>0||s>0?ri(t.left+p,t.top+2*U/3,i-2*F/3,a-2*U/3,bo.TOP_RIGHT):new $o(t.left+t.width-2*F/3,t.top+2*U/3),this.bottomRightBorderDoubleInnerBox=c>0||B>0?ri(t.left+h,t.top+Q,c-2*F/3,B-2*y/3,bo.BOTTOM_RIGHT):new $o(t.left+t.width-2*F/3,t.top+t.height-2*y/3),this.bottomLeftBorderDoubleInnerBox=d>0||g>0?ri(t.left+2*v/3,t.top+C,d-2*v/3,g-2*y/3,bo.BOTTOM_LEFT):new $o(t.left+2*v/3,t.top+t.height-2*y/3),this.topLeftBorderStroke=n>0||s>0?ri(t.left+v/2,t.top+U/2,n-v/2,s-U/2,bo.TOP_LEFT):new $o(t.left+v/2,t.top+U/2),this.topRightBorderStroke=n>0||s>0?ri(t.left+p,t.top+U/2,i-F/2,a-U/2,bo.TOP_RIGHT):new $o(t.left+t.width-F/2,t.top+U/2),this.bottomRightBorderStroke=c>0||B>0?ri(t.left+h,t.top+Q,c-F/2,B-y/2,bo.BOTTOM_RIGHT):new $o(t.left+t.width-F/2,t.top+t.height-y/2),this.bottomLeftBorderStroke=d>0||g>0?ri(t.left+v/2,t.top+C,d-v/2,g-y/2,bo.BOTTOM_LEFT):new $o(t.left+v/2,t.top+t.height-y/2),this.topLeftBorderBox=n>0||s>0?ri(t.left,t.top,n,s,bo.TOP_LEFT):new $o(t.left,t.top),this.topRightBorderBox=i>0||a>0?ri(t.left+p,t.top,i,a,bo.TOP_RIGHT):new $o(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||B>0?ri(t.left+h,t.top+Q,c,B,bo.BOTTOM_RIGHT):new $o(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=d>0||g>0?ri(t.left,t.top+C,d,g,bo.BOTTOM_LEFT):new $o(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||s>0?ri(t.left+v,t.top+U,Math.max(0,n-v),Math.max(0,s-U),bo.TOP_LEFT):new $o(t.left+v,t.top+U),this.topRightPaddingBox=i>0||a>0?ri(t.left+Math.min(p,t.width-F),t.top+U,p>t.width+F?0:Math.max(0,i-F),Math.max(0,a-U),bo.TOP_RIGHT):new $o(t.left+t.width-F,t.top+U),this.bottomRightPaddingBox=c>0||B>0?ri(t.left+Math.min(h,t.width-v),t.top+Math.min(Q,t.height-y),Math.max(0,c-F),Math.max(0,B-y),bo.BOTTOM_RIGHT):new $o(t.left+t.width-F,t.top+t.height-y),this.bottomLeftPaddingBox=d>0||g>0?ri(t.left+v,t.top+Math.min(C,t.height-y),Math.max(0,d-v),Math.max(0,g-y),bo.BOTTOM_LEFT):new $o(t.left+v,t.top+t.height-y),this.topLeftContentBox=n>0||s>0?ri(t.left+v+H,t.top+U+m,Math.max(0,n-(v+H)),Math.max(0,s-(U+m)),bo.TOP_LEFT):new $o(t.left+v+H,t.top+U+m),this.topRightContentBox=i>0||a>0?ri(t.left+Math.min(p,t.width+v+H),t.top+U+m,p>t.width+v+H?0:i-v+H,a-(U+m),bo.TOP_RIGHT):new $o(t.left+t.width-(F+b),t.top+U+m),this.bottomRightContentBox=c>0||B>0?ri(t.left+Math.min(h,t.width-(v+H)),t.top+Math.min(Q,t.height+U+m),Math.max(0,c-(F+b)),B-(y+E),bo.BOTTOM_RIGHT):new $o(t.left+t.width-(F+b),t.top+t.height-(y+E)),this.bottomLeftContentBox=d>0||g>0?ri(t.left+v+H,t.top+C,Math.max(0,d-(v+H)),g-(y+E),bo.BOTTOM_LEFT):new $o(t.left+v+H,t.top+t.height-(y+E))}}();(Eo=bo||(bo={}))[Eo.TOP_LEFT=0]="TOP_LEFT",Eo[Eo.TOP_RIGHT=1]="TOP_RIGHT",Eo[Eo.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",Eo[Eo.BOTTOM_LEFT=3]="BOTTOM_LEFT";var ri=function(A,e,t,r,n){var s=(Math.sqrt(2)-1)/3*4,o=t*s,i=r*s,a=A+t,l=e+r;switch(n){case bo.TOP_LEFT:return new Ai(new $o(A,l),new $o(A,l-i),new $o(a-o,e),new $o(a,e));case bo.TOP_RIGHT:return new Ai(new $o(A,e),new $o(A+o,e),new $o(a,l-i),new $o(a,l));case bo.BOTTOM_RIGHT:return new Ai(new $o(a,e),new $o(a,e+i),new $o(A+o,l),new $o(A,l));case bo.BOTTOM_LEFT:default:return new Ai(new $o(a,l),new $o(a-o,l),new $o(A,e+i),new $o(A,e))}},ni=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},si=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},oi=function(){return function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6}}(),ii=function(){return function(A,e){this.path=A,this.target=e,this.type=1}}(),ai=function(){return function(A){this.opacity=A,this.type=2,this.target=6}}(),li=function(A){return 1===A.type},ci=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},Bi=function(){return function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}}(),ui=function(){function A(A,e){if(this.container=A,this.parent=e,this.effects=[],this.curves=new ti(this.container),this.container.styles.opacity<1&&this.effects.push(new ai(this.container.styles.opacity)),null!==this.container.styles.transform){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,r=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform;this.effects.push(new oi(t,r,n))}if(0!==this.container.styles.overflowX){var s=ni(this.curves),o=si(this.curves);ci(s,o)?this.effects.push(new ii(s,6)):(this.effects.push(new ii(s,2)),this.effects.push(new ii(o,4)))}}return A.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n=t.effects.filter((function(A){return!li(A)}));if(e||0!==t.container.styles.position||!t.parent){if(r.unshift.apply(r,n),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX){var s=ni(t.curves),o=si(t.curves);ci(s,o)||r.unshift(new ii(o,6))}}else r.unshift.apply(r,n);t=t.parent}return r.filter((function(e){return mn(e.target,A)}))},A}(),di=function(A,e,t,r){A.container.elements.forEach((function(n){var s=mn(n.flags,4),o=mn(n.flags,2),i=new ui(n,A);mn(n.styles.display,2048)&&r.push(i);var a=mn(n.flags,8)?[]:r;if(s||o){var l=s||n.styles.isPositioned()?t:e,c=new Bi(i);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var B=n.styles.zIndex.order;if(B<0){var u=0;l.negativeZIndex.some((function(A,e){return B>A.element.container.styles.zIndex.order?(u=e,!1):u>0})),l.negativeZIndex.splice(u,0,c)}else if(B>0){var d=0;l.positiveZIndex.some((function(A,e){return B>=A.element.container.styles.zIndex.order?(d=e+1,!1):d>0})),l.positiveZIndex.splice(d,0,c)}else l.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?l.nonPositionedFloats.push(c):l.nonPositionedInlineLevel.push(c);di(i,c,s?c:t,a)}else n.styles.isInlineLevel()?e.inlineLevel.push(i):e.nonInlineLevel.push(i),di(i,e,t,a);mn(n.flags,8)&&gi(n,a)}))},gi=function(A,e){for(var t=A instanceof Es?A.start:1,r=A instanceof Es&&A.reversed,n=0;n<e.length;n++){var s=e[n];s.container instanceof bs&&"number"==typeof s.container.value&&0!==s.container.value&&(t=s.container.value),s.listValue=yo(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},wi=function(A,e){switch(e){case 0:return pi(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return pi(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return pi(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return pi(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},fi=function(A,e){var t=[];return ei(A)?t.push(A.subdivide(.5,!1)):t.push(A),ei(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},pi=function(A,e,t,r){var n=[];return ei(A)?n.push(A.subdivide(.5,!1)):n.push(A),ei(t)?n.push(t.subdivide(.5,!0)):n.push(t),ei(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),ei(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},Qi=function(A){var e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},hi=function(A){var e=A.styles,t=A.bounds,r=pt(e.paddingLeft,t.width),n=pt(e.paddingRight,t.width),s=pt(e.paddingTop,t.width),o=pt(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,s+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+s+o))},Ci=function(A,e,t){var r,n,s=(r=vi(A.styles.backgroundOrigin,e),n=A,0===r?n.bounds:2===r?hi(n):Qi(n)),o=function(A,e){return 0===A?e.bounds:2===A?hi(e):Qi(e)}(vi(A.styles.backgroundClip,e),A),i=yi(vi(A.styles.backgroundSize,e),t,s),a=i[0],l=i[1],c=ft(vi(A.styles.backgroundPosition,e),s.width-a,s.height-l);return[mi(vi(A.styles.backgroundRepeat,e),c,i,s,o),Math.round(s.left+c[0]),Math.round(s.top+c[1]),a,l]},Ui=function(A){return rt(A)&&A.value===qt.AUTO},Fi=function(A){return"number"==typeof A},yi=function(A,e,t){var r=e[0],n=e[1],s=e[2],o=A[0],i=A[1];if(!o)return[0,0];if(Bt(o)&&i&&Bt(i))return[pt(o,t.width),pt(i,t.height)];var a=Fi(s);if(rt(o)&&(o.value===qt.CONTAIN||o.value===qt.COVER))return Fi(s)?t.width/t.height<s!=(o.value===qt.COVER)?[t.width,t.width/s]:[t.height*s,t.height]:[t.width,t.height];var l=Fi(r),c=Fi(n),B=l||c;if(Ui(o)&&(!i||Ui(i)))return l&&c?[r,n]:a||B?B&&a?[l?r:n*s,c?n:r/s]:[l?r:t.width,c?n:t.height]:[t.width,t.height];if(a){var u=0,d=0;return Bt(o)?u=pt(o,t.width):Bt(i)&&(d=pt(i,t.height)),Ui(o)?u=d*s:i&&!Ui(i)||(d=u/s),[u,d]}var g=null,w=null;if(Bt(o)?g=pt(o,t.width):i&&Bt(i)&&(w=pt(i,t.height)),null===g||i&&!Ui(i)||(w=l&&c?g/r*n:t.height),null!==w&&Ui(o)&&(g=l&&c?w/n*r:t.width),null!==g&&null!==w)return[g,w];throw new Error("Unable to calculate background-size for element")},vi=function(A,e){var t=A[e];return void 0===t?A[0]:t},mi=function(A,e,t,r,n){var s=e[0],o=e[1],i=t[0],a=t[1];switch(A){case 2:return[new $o(Math.round(r.left),Math.round(r.top+o)),new $o(Math.round(r.left+r.width),Math.round(r.top+o)),new $o(Math.round(r.left+r.width),Math.round(a+r.top+o)),new $o(Math.round(r.left),Math.round(a+r.top+o))];case 3:return[new $o(Math.round(r.left+s),Math.round(r.top)),new $o(Math.round(r.left+s+i),Math.round(r.top)),new $o(Math.round(r.left+s+i),Math.round(r.height+r.top)),new $o(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new $o(Math.round(r.left+s),Math.round(r.top+o)),new $o(Math.round(r.left+s+i),Math.round(r.top+o)),new $o(Math.round(r.left+s+i),Math.round(r.top+o+a)),new $o(Math.round(r.left+s),Math.round(r.top+o+a))];default:return[new $o(Math.round(n.left),Math.round(n.top)),new $o(Math.round(n.left+n.width),Math.round(n.top)),new $o(Math.round(n.left+n.width),Math.round(n.height+n.top)),new $o(Math.round(n.left),Math.round(n.height+n.top))]}},bi="Hidden Text",Ei=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),s=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",s.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(bi)),t.appendChild(n),t.appendChild(r);var o=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(bi)),t.style.lineHeight="normal",r.style.verticalAlign="super";var i=r.offsetTop-t.offsetTop+2;return s.removeChild(t),{baseline:o,middle:i}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),Hi=function(){return function(A,e){this.context=A,this.options=e}}(),Ii=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new Ei(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return sA(e,A),e.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},e.prototype.applyEffect=function(A){this.ctx.save(),function(A){return 2===A.type}(A)&&(this.ctx.globalAlpha=A.opacity),function(A){return 0===A.type}(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),li(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return iA(this,0,void 0,(function(){return aA(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.renderNode=function(A){return iA(this,0,void 0,(function(){return aA(this,(function(e){switch(e.label){case 0:return mn(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},e.prototype.renderTextWithLetterSpacing=function(A,e,t){var r=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):ws(A.text).reduce((function(e,n){return r.ctx.fillText(n,e,A.bounds.top+t),e+r.ctx.measureText(n).width}),A.bounds.left)},e.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Si(A.fontFamily).join(", "),r=et(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},e.prototype.renderTextNode=function(A,e){return iA(this,0,void 0,(function(){var t,r,n,s,o,i,a,l,c=this;return aA(this,(function(B){return t=this.createFontStyle(e),r=t[0],n=t[1],s=t[2],this.ctx.font=r,this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",o=this.fontMetrics.getMetrics(n,s),i=o.baseline,a=o.middle,l=e.paintOrder,A.textBounds.forEach((function(A){l.forEach((function(t){switch(t){case 0:c.ctx.fillStyle=bt(e.color),c.renderTextWithLetterSpacing(A,e.letterSpacing,i);var r=e.textShadow;r.length&&A.text.trim().length&&(r.slice(0).reverse().forEach((function(t){c.ctx.shadowColor=bt(t.color),c.ctx.shadowOffsetX=t.offsetX.number*c.options.scale,c.ctx.shadowOffsetY=t.offsetY.number*c.options.scale,c.ctx.shadowBlur=t.blur.number,c.renderTextWithLetterSpacing(A,e.letterSpacing,i)})),c.ctx.shadowColor="",c.ctx.shadowOffsetX=0,c.ctx.shadowOffsetY=0,c.ctx.shadowBlur=0),e.textDecorationLine.length&&(c.ctx.fillStyle=bt(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+i),A.bounds.width,1);break;case 2:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:c.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+a),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(c.ctx.strokeStyle=bt(e.webkitTextStrokeColor),c.ctx.lineWidth=e.webkitTextStrokeWidth,c.ctx.lineJoin=window.chrome?"miter":"round",c.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+i)),c.ctx.strokeStyle="",c.ctx.lineWidth=0,c.ctx.lineJoin="miter"}}))})),[2]}))}))},e.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=hi(A),n=si(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return iA(this,0,void 0,(function(){var t,r,n,s,o,i,a,l,c,B,u,d,g,w,f,p,Q,h;return aA(this,(function(C){switch(C.label){case 0:this.applyEffects(A.getEffects(4)),t=A.container,r=A.curves,n=t.styles,s=0,o=t.textNodes,C.label=1;case 1:return s<o.length?(i=o[s],[4,this.renderTextNode(i,n)]):[3,4];case 2:C.sent(),C.label=3;case 3:return s++,[3,1];case 4:if(!(t instanceof ys))return[3,8];C.label=5;case 5:return C.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return f=C.sent(),this.renderReplacedElement(t,r,f),[3,8];case 7:return C.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof vs&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof ms))return[3,12];C.label=9;case 9:return C.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return f=C.sent(),this.renderReplacedElement(t,r,f),[3,12];case 11:return C.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Os&&t.tree?[4,new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}).render(t.tree)]:[3,14];case 13:a=C.sent(),t.width&&t.height&&this.ctx.drawImage(a,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),C.label=14;case 14:if(t instanceof Ss&&(l=Math.min(t.bounds.width,t.bounds.height),t.type===xs?t.checked&&(this.ctx.save(),this.path([new $o(t.bounds.left+.39363*l,t.bounds.top+.79*l),new $o(t.bounds.left+.16*l,t.bounds.top+.5549*l),new $o(t.bounds.left+.27347*l,t.bounds.top+.44071*l),new $o(t.bounds.left+.39694*l,t.bounds.top+.5649*l),new $o(t.bounds.left+.72983*l,t.bounds.top+.23*l),new $o(t.bounds.left+.84*l,t.bounds.top+.34085*l),new $o(t.bounds.left+.39363*l,t.bounds.top+.79*l)]),this.ctx.fillStyle=bt(Ds),this.ctx.fill(),this.ctx.restore()):t.type===Ks&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+l/2,t.bounds.top+l/2,l/4,0,2*Math.PI,!0),this.ctx.fillStyle=bt(Ds),this.ctx.fill(),this.ctx.restore())),xi(t)&&t.value.length){switch(c=this.createFontStyle(n),Q=c[0],B=c[1],u=this.fontMetrics.getMetrics(Q,B).baseline,this.ctx.font=Q,this.ctx.fillStyle=bt(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Li(t.styles.textAlign),h=hi(t),d=0,t.styles.textAlign){case 1:d+=h.width/2;break;case 2:d+=h.width}g=h.add(d,0,0,-h.height/2+1),this.ctx.save(),this.path([new $o(h.left,h.top),new $o(h.left+h.width,h.top),new $o(h.left+h.width,h.top+h.height),new $o(h.left,h.top+h.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new us(t.value,g),n.letterSpacing,u),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!mn(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(0!==(w=t.styles.listStyleImage).type)return[3,18];f=void 0,p=w.url,C.label=15;case 15:return C.trys.push([15,17,,18]),[4,this.context.cache.match(p)];case 16:return f=C.sent(),this.ctx.drawImage(f,t.bounds.left-(f.width+10),t.bounds.top),[3,18];case 17:return C.sent(),this.context.logger.error("Error loading list-style-image "+p),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==t.styles.listStyleType&&(Q=this.createFontStyle(n)[0],this.ctx.font=Q,this.ctx.fillStyle=bt(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",h=new cA(t.bounds.left,t.bounds.top+pt(t.styles.paddingTop,t.bounds.width),t.bounds.width,Pr(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new us(A.listValue,h),n.letterSpacing,Pr(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),C.label=20;case 20:return[2]}}))}))},e.prototype.renderStackContent=function(A){return iA(this,0,void 0,(function(){var e,t,r,n,s,o,i,a,l,c,B,u,d,g,w;return aA(this,(function(f){switch(f.label){case 0:return mn(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:f.sent(),e=0,t=A.negativeZIndex,f.label=2;case 2:return e<t.length?(w=t[e],[4,this.renderStack(w)]):[3,5];case 3:f.sent(),f.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:f.sent(),r=0,n=A.nonInlineLevel,f.label=7;case 7:return r<n.length?(w=n[r],[4,this.renderNode(w)]):[3,10];case 8:f.sent(),f.label=9;case 9:return r++,[3,7];case 10:s=0,o=A.nonPositionedFloats,f.label=11;case 11:return s<o.length?(w=o[s],[4,this.renderStack(w)]):[3,14];case 12:f.sent(),f.label=13;case 13:return s++,[3,11];case 14:i=0,a=A.nonPositionedInlineLevel,f.label=15;case 15:return i<a.length?(w=a[i],[4,this.renderStack(w)]):[3,18];case 16:f.sent(),f.label=17;case 17:return i++,[3,15];case 18:l=0,c=A.inlineLevel,f.label=19;case 19:return l<c.length?(w=c[l],[4,this.renderNode(w)]):[3,22];case 20:f.sent(),f.label=21;case 21:return l++,[3,19];case 22:B=0,u=A.zeroOrAutoZIndexOrTransformedOrOpacity,f.label=23;case 23:return B<u.length?(w=u[B],[4,this.renderStack(w)]):[3,26];case 24:f.sent(),f.label=25;case 25:return B++,[3,23];case 26:d=0,g=A.positiveZIndex,f.label=27;case 27:return d<g.length?(w=g[d],[4,this.renderStack(w)]):[3,30];case 28:f.sent(),f.label=29;case 29:return d++,[3,27];case 30:return[2]}}))}))},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=ei(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),ei(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},e.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},e.prototype.resizeImage=function(A,e,t){var r;if(A.width===e&&A.height===t)return A;var n=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},e.prototype.renderBackgroundImage=function(A){return iA(this,0,void 0,(function(){var e,t,r,n,s,o;return aA(this,(function(i){switch(i.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var n,s,o,i,a,l,c,B,u,d,g,w,f,p,Q,h,C,U,F,y,v,m,b,E,H,I,x,K,L,D,S;return aA(this,(function(T){switch(T.label){case 0:if(0!==t.type)return[3,5];n=void 0,s=t.url,T.label=1;case 1:return T.trys.push([1,3,,4]),[4,r.context.cache.match(s)];case 2:return n=T.sent(),[3,4];case 3:return T.sent(),r.context.logger.error("Error loading background-image "+s),[3,4];case 4:return n&&(o=Ci(A,e,[n.width,n.height,n.width/n.height]),h=o[0],m=o[1],b=o[2],F=o[3],y=o[4],p=r.ctx.createPattern(r.resizeImage(n,F,y),"repeat"),r.renderRepeat(h,p,m,b)),[3,6];case 5:1===t.type?(i=Ci(A,e,[null,null,null]),h=i[0],m=i[1],b=i[2],F=i[3],y=i[4],a=_t(t.angle,F,y),l=a[0],c=a[1],B=a[2],u=a[3],d=a[4],(g=document.createElement("canvas")).width=F,g.height=y,w=g.getContext("2d"),f=w.createLinearGradient(c,u,B,d),kt(t.stops,l).forEach((function(A){return f.addColorStop(A.stop,bt(A.color))})),w.fillStyle=f,w.fillRect(0,0,F,y),F>0&&y>0&&(p=r.ctx.createPattern(g,"repeat"),r.renderRepeat(h,p,m,b))):function(A){return 2===A.type}(t)&&(Q=Ci(A,e,[null,null,null]),h=Q[0],C=Q[1],U=Q[2],F=Q[3],y=Q[4],v=0===t.position.length?[gt]:t.position,m=pt(v[0],F),b=pt(v[v.length-1],y),E=function(A,e,t,r,n){var s=0,o=0;switch(A.size){case 0:0===A.shape?s=o=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.min(Math.abs(e),Math.abs(e-r)),o=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(0===A.shape)s=o=Math.min(Rt(e,t),Rt(e,t-n),Rt(e-r,t),Rt(e-r,t-n));else if(1===A.shape){var i=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),a=Pt(r,n,e,t,!0),l=a[0],c=a[1];o=i*(s=Rt(l-e,(c-t)/i))}break;case 1:0===A.shape?s=o=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(s=Math.max(Math.abs(e),Math.abs(e-r)),o=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(0===A.shape)s=o=Math.max(Rt(e,t),Rt(e,t-n),Rt(e-r,t),Rt(e-r,t-n));else if(1===A.shape){i=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var B=Pt(r,n,e,t,!1);l=B[0],c=B[1],o=i*(s=Rt(l-e,(c-t)/i))}}return Array.isArray(A.size)&&(s=pt(A.size[0],r),o=2===A.size.length?pt(A.size[1],n):s),[s,o]}(t,m,b,F,y),H=E[0],I=E[1],H>0&&I>0&&(x=r.ctx.createRadialGradient(C+m,U+b,0,C+m,U+b,H),kt(t.stops,2*H).forEach((function(A){return x.addColorStop(A.stop,bt(A.color))})),r.path(h),r.ctx.fillStyle=x,H!==I?(K=A.bounds.left+.5*A.bounds.width,L=A.bounds.top+.5*A.bounds.height,S=1/(D=I/H),r.ctx.save(),r.ctx.translate(K,L),r.ctx.transform(1,0,0,D,0,0),r.ctx.translate(-K,-L),r.ctx.fillRect(C,S*(U-L)+L,F,y*S),r.ctx.restore()):r.ctx.fill())),T.label=6;case 6:return e--,[2]}}))},r=this,n=0,s=A.styles.backgroundImage.slice(0).reverse(),i.label=1;case 1:return n<s.length?(o=s[n],[5,t(o)]):[3,4];case 2:i.sent(),i.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},e.prototype.renderSolidBorder=function(A,e,t){return iA(this,0,void 0,(function(){return aA(this,(function(r){return this.path(wi(t,e)),this.ctx.fillStyle=bt(A),this.ctx.fill(),[2]}))}))},e.prototype.renderDoubleBorder=function(A,e,t,r){return iA(this,0,void 0,(function(){var n,s;return aA(this,(function(o){switch(o.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,r)]:[3,2];case 1:return o.sent(),[2];case 2:return n=function(A,e){switch(e){case 0:return pi(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return pi(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return pi(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return pi(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(r,t),this.path(n),this.ctx.fillStyle=bt(A),this.ctx.fill(),s=function(A,e){switch(e){case 0:return pi(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return pi(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return pi(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return pi(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(r,t),this.path(s),this.ctx.fill(),[2]}}))}))},e.prototype.renderNodeBackgroundAndBorders=function(A){return iA(this,0,void 0,(function(){var e,t,r,n,s,o,i,a,l=this;return aA(this,(function(c){switch(c.label){case 0:return this.applyEffects(A.getEffects(2)),e=A.container.styles,t=!mt(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=Ki(vi(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),mt(e.backgroundColor)||(this.ctx.fillStyle=bt(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:c.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){l.ctx.save();var t,r,n,s,o,i=ni(A.curves),a=e.inset?0:1e4,c=(t=i,r=-a+(e.inset?1:-1)*e.spread.number,n=(e.inset?1:-1)*e.spread.number,s=e.spread.number*(e.inset?-2:2),o=e.spread.number*(e.inset?-2:2),t.map((function(A,e){switch(e){case 0:return A.add(r,n);case 1:return A.add(r+s,n);case 2:return A.add(r+s,n+o);case 3:return A.add(r,n+o)}return A})));e.inset?(l.path(i),l.ctx.clip(),l.mask(c)):(l.mask(i),l.ctx.clip(),l.path(c)),l.ctx.shadowOffsetX=e.offsetX.number+a,l.ctx.shadowOffsetY=e.offsetY.number,l.ctx.shadowColor=bt(e.color),l.ctx.shadowBlur=e.blur.number,l.ctx.fillStyle=e.inset?bt(e.color):"rgba(0,0,0,1)",l.ctx.fill(),l.ctx.restore()})),c.label=2;case 2:s=0,o=0,i=r,c.label=3;case 3:return o<i.length?0!==(a=i[o]).style&&!mt(a.color)&&a.width>0?2!==a.style?[3,5]:[4,this.renderDashedDottedBorder(a.color,a.width,s,A.curves,2)]:[3,11]:[3,13];case 4:return c.sent(),[3,11];case 5:return 3!==a.style?[3,7]:[4,this.renderDashedDottedBorder(a.color,a.width,s,A.curves,3)];case 6:return c.sent(),[3,11];case 7:return 4!==a.style?[3,9]:[4,this.renderDoubleBorder(a.color,a.width,s,A.curves)];case 8:return c.sent(),[3,11];case 9:return[4,this.renderSolidBorder(a.color,s,A.curves)];case 10:c.sent(),c.label=11;case 11:s++,c.label=12;case 12:return o++,[3,3];case 13:return[2]}}))}))},e.prototype.renderDashedDottedBorder=function(A,e,t,r,n){return iA(this,0,void 0,(function(){var s,o,i,a,l,c,B,u,d,g,w,f,p,Q,h,C;return aA(this,(function(U){return this.ctx.save(),s=function(A,e){switch(e){case 0:return fi(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return fi(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return fi(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return fi(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(r,t),o=wi(r,t),2===n&&(this.path(o),this.ctx.clip()),ei(o[0])?(i=o[0].start.x,a=o[0].start.y):(i=o[0].x,a=o[0].y),ei(o[1])?(l=o[1].end.x,c=o[1].end.y):(l=o[1].x,c=o[1].y),B=0===t||2===t?Math.abs(i-l):Math.abs(a-c),this.ctx.beginPath(),3===n?this.formatPath(s):this.formatPath(o.slice(0,2)),u=e<3?3*e:2*e,d=e<3?2*e:e,3===n&&(u=e,d=e),g=!0,B<=2*u?g=!1:B<=2*u+d?(u*=w=B/(2*u+d),d*=w):(f=Math.floor((B+d)/(u+d)),p=(B-f*u)/(f-1),d=(Q=(B-(f+1)*u)/f)<=0||Math.abs(d-p)<Math.abs(d-Q)?p:Q),g&&(3===n?this.ctx.setLineDash([0,u+d]):this.ctx.setLineDash([u,d])),3===n?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=bt(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===n&&(ei(o[0])&&(h=o[3],C=o[0],this.ctx.beginPath(),this.formatPath([new $o(h.end.x,h.end.y),new $o(C.start.x,C.start.y)]),this.ctx.stroke()),ei(o[1])&&(h=o[1],C=o[2],this.ctx.beginPath(),this.formatPath([new $o(h.end.x,h.end.y),new $o(C.start.x,C.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},e.prototype.render=function(A){return iA(this,0,void 0,(function(){var e;return aA(this,(function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=bt(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=new ui(A,null),n=new Bi(r),di(r,n,n,s=[]),gi(r.container,s),e=n,[4,this.renderStack(e)];case 1:return t.sent(),this.applyEffects([]),[2,this.canvas]}var r,n,s}))}))},e}(Hi),xi=function(A){return A instanceof Ms||(A instanceof Ts||A instanceof Ss&&A.type!==Ks&&A.type!==xs)},Ki=function(A,e){switch(A){case 0:return ni(e);case 2:return function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]}(e);default:return si(e)}},Li=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},Di=["-apple-system","system-ui"],Si=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===Di.indexOf(A)})):A},Ti=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return sA(e,A),e.prototype.render=function(A){return iA(this,0,void 0,(function(){var e,t;return aA(this,(function(r){switch(r.label){case 0:return e=ls(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,Mi(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=bt(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},e}(Hi),Mi=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Oi=function(){function A(A){var e=A.id,t=A.enabled;this.id=e,this.enabled=t,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,lA([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,lA([this.id,this.getTime()+"ms"],A))},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,lA([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,lA([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.instances={},A}(),ki=function(){function A(e,t){var r;this.windowBounds=t,this.instanceName="#"+A.instanceCount++,this.logger=new Oi({id:this.instanceName,enabled:e.logging}),this.cache=null!==(r=e.cache)&&void 0!==r?r:new No(this,e)}return A.instanceCount=1,A}();"undefined"!=typeof window&&Vo.setContext(window);var _i=function(A,e){return iA(void 0,0,void 0,(function(){var t,r,n,s,o,i,a,l,c,B,u,d,g,w,f,p,Q,h,C,U,F,y,v,m,b,E,H,I,x,K,L,D,S,T,M,O,k,_;return aA(this,(function(R){switch(R.label){case 0:if(!A||"object"!=typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(!(t=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(r=t.defaultView))throw new Error("Document is not attached to a Window");return n={allowTaint:null!==(y=e.allowTaint)&&void 0!==y&&y,imageTimeout:null!==(v=e.imageTimeout)&&void 0!==v?v:15e3,proxy:e.proxy,useCORS:null!==(m=e.useCORS)&&void 0!==m&&m},s=oA({logging:null===(b=e.logging)||void 0===b||b,cache:e.cache},n),o={windowWidth:null!==(E=e.windowWidth)&&void 0!==E?E:r.innerWidth,windowHeight:null!==(H=e.windowHeight)&&void 0!==H?H:r.innerHeight,scrollX:null!==(I=e.scrollX)&&void 0!==I?I:r.pageXOffset,scrollY:null!==(x=e.scrollY)&&void 0!==x?x:r.pageYOffset},i=new cA(o.scrollX,o.scrollY,o.windowWidth,o.windowHeight),a=new ki(s,i),l=null!==(K=e.foreignObjectRendering)&&void 0!==K&&K,c={allowTaint:null!==(L=e.allowTaint)&&void 0!==L&&L,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:l,copyStyles:l},a.logger.debug("Starting document clone with size "+i.width+"x"+i.height+" scrolled to "+-i.left+","+-i.top),B=new mo(a,A,c),(u=B.clonedReferenceElement)?[4,B.toIFrame(t,i)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return d=R.sent(),g=$s(u)||"HTML"===u.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new cA(0,0,r,n)}(u.ownerDocument):BA(a,u),w=g.width,f=g.height,p=g.left,Q=g.top,h=Ri(a,u,e.backgroundColor),C={canvas:e.canvas,backgroundColor:h,scale:null!==(S=null!==(D=e.scale)&&void 0!==D?D:r.devicePixelRatio)&&void 0!==S?S:1,x:(null!==(T=e.x)&&void 0!==T?T:0)+p,y:(null!==(M=e.y)&&void 0!==M?M:0)+Q,width:null!==(O=e.width)&&void 0!==O?O:Math.ceil(w),height:null!==(k=e.height)&&void 0!==k?k:Math.ceil(f)},l?(a.logger.debug("Document cloned, using foreign object rendering"),[4,new Ti(a,C).render(u)]):[3,3];case 2:return U=R.sent(),[3,5];case 3:return a.logger.debug("Document cloned, element located at "+p+","+Q+" with size "+w+"x"+f+" using computed rendering"),a.logger.debug("Starting DOM parsing"),F=Ps(a,u),h===F.styles.backgroundColor&&(F.styles.backgroundColor=St.TRANSPARENT),a.logger.debug("Starting renderer for element at "+C.x+","+C.y+" with size "+C.width+"x"+C.height),[4,new Ii(a,C).render(F)];case 4:U=R.sent(),R.label=5;case 5:return(null===(_=e.removeContainer)||void 0===_||_)&&(mo.destroy(d)||a.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),a.logger.debug("Finished rendering"),[2,U]}}))}))},Ri=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?Dt(A,getComputedStyle(r.documentElement).backgroundColor):St.TRANSPARENT,s=r.body?Dt(A,getComputedStyle(r.body).backgroundColor):St.TRANSPARENT,o="string"==typeof t?Dt(A,t):null===t?St.TRANSPARENT:4294967295;return e===r.documentElement?mt(n)?mt(s)?o:s:n:o},Pi={},Vi={},Ni={};let Gi;const Ji=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];Ni.getSymbolSize=function(A){if(!A)throw new Error('"version" cannot be null or undefined');if(A<1||A>40)throw new Error('"version" should be in range from 1 to 40');return 4*A+17},Ni.getSymbolTotalCodewords=function(A){return Ji[A]},Ni.getBCHDigit=function(A){let e=0;for(;0!==A;)e++,A>>>=1;return e},Ni.setToSJISFunction=function(A){if("function"!=typeof A)throw new Error('"toSJISFunc" is not a valid function.');Gi=A},Ni.isKanjiModeEnabled=function(){return void 0!==Gi},Ni.toSJIS=function(A){return Gi(A)};var Xi,Yi={};function Wi(){this.buffer=[],this.length=0}(Xi=Yi).L={bit:1},Xi.M={bit:0},Xi.Q={bit:3},Xi.H={bit:2},Xi.isValid=function(A){return A&&void 0!==A.bit&&A.bit>=0&&A.bit<4},Xi.from=function(A,e){if(Xi.isValid(A))return A;try{return function(A){if("string"!=typeof A)throw new Error("Param is not a string");switch(A.toLowerCase()){case"l":case"low":return Xi.L;case"m":case"medium":return Xi.M;case"q":case"quartile":return Xi.Q;case"h":case"high":return Xi.H;default:throw new Error("Unknown EC Level: "+A)}}(A)}catch(t){return e}},Wi.prototype={get:function(A){const e=Math.floor(A/8);return 1==(this.buffer[e]>>>7-A%8&1)},put:function(A,e){for(let t=0;t<e;t++)this.putBit(1==(A>>>e-t-1&1))},getLengthInBits:function(){return this.length},putBit:function(A){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),A&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Zi=Wi;function ji(A){if(!A||A<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=A,this.data=new Uint8Array(A*A),this.reservedBit=new Uint8Array(A*A)}ji.prototype.set=function(A,e,t,r){const n=A*this.size+e;this.data[n]=t,r&&(this.reservedBit[n]=!0)},ji.prototype.get=function(A,e){return this.data[A*this.size+e]},ji.prototype.xor=function(A,e,t){this.data[A*this.size+e]^=t},ji.prototype.isReserved=function(A,e){return this.reservedBit[A*this.size+e]};var zi=ji,$i={};!function(A){const e=Ni.getSymbolSize;A.getRowColCoords=function(A){if(1===A)return[];const t=Math.floor(A/7)+2,r=e(A),n=145===r?26:2*Math.ceil((r-13)/(2*t-2)),s=[r-7];for(let e=1;e<t-1;e++)s[e]=s[e-1]-n;return s.push(6),s.reverse()},A.getPositions=function(e){const t=[],r=A.getRowColCoords(e),n=r.length;for(let A=0;A<n;A++)for(let e=0;e<n;e++)0===A&&0===e||0===A&&e===n-1||A===n-1&&0===e||t.push([r[A],r[e]]);return t}}($i);var qi={};const Aa=Ni.getSymbolSize;qi.getPositions=function(A){const e=Aa(A);return[[0,0],[e-7,0],[0,e-7]]};var ea={};!function(A){A.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,t=3,r=40,n=10;function s(e,t,r){switch(e){case A.Patterns.PATTERN000:return(t+r)%2==0;case A.Patterns.PATTERN001:return t%2==0;case A.Patterns.PATTERN010:return r%3==0;case A.Patterns.PATTERN011:return(t+r)%3==0;case A.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case A.Patterns.PATTERN101:return t*r%2+t*r%3==0;case A.Patterns.PATTERN110:return(t*r%2+t*r%3)%2==0;case A.Patterns.PATTERN111:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}A.isValid=function(A){return null!=A&&""!==A&&!isNaN(A)&&A>=0&&A<=7},A.from=function(e){return A.isValid(e)?parseInt(e,10):void 0},A.getPenaltyN1=function(A){const t=A.size;let r=0,n=0,s=0,o=null,i=null;for(let a=0;a<t;a++){n=s=0,o=i=null;for(let l=0;l<t;l++){let t=A.get(a,l);t===o?n++:(n>=5&&(r+=e+(n-5)),o=t,n=1),t=A.get(l,a),t===i?s++:(s>=5&&(r+=e+(s-5)),i=t,s=1)}n>=5&&(r+=e+(n-5)),s>=5&&(r+=e+(s-5))}return r},A.getPenaltyN2=function(A){const e=A.size;let r=0;for(let t=0;t<e-1;t++)for(let n=0;n<e-1;n++){const e=A.get(t,n)+A.get(t,n+1)+A.get(t+1,n)+A.get(t+1,n+1);4!==e&&0!==e||r++}return r*t},A.getPenaltyN3=function(A){const e=A.size;let t=0,n=0,s=0;for(let r=0;r<e;r++){n=s=0;for(let o=0;o<e;o++)n=n<<1&2047|A.get(r,o),o>=10&&(1488===n||93===n)&&t++,s=s<<1&2047|A.get(o,r),o>=10&&(1488===s||93===s)&&t++}return t*r},A.getPenaltyN4=function(A){let e=0;const t=A.data.length;for(let r=0;r<t;r++)e+=A.data[r];return Math.abs(Math.ceil(100*e/t/5)-10)*n},A.applyMask=function(A,e){const t=e.size;for(let r=0;r<t;r++)for(let n=0;n<t;n++)e.isReserved(n,r)||e.xor(n,r,s(A,n,r))},A.getBestMask=function(e,t){const r=Object.keys(A.Patterns).length;let n=0,s=1/0;for(let o=0;o<r;o++){t(o),A.applyMask(o,e);const r=A.getPenaltyN1(e)+A.getPenaltyN2(e)+A.getPenaltyN3(e)+A.getPenaltyN4(e);A.applyMask(o,e),r<s&&(s=r,n=o)}return n}}(ea);var ta={};const ra=Yi,na=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],sa=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];ta.getBlocksCount=function(A,e){switch(e){case ra.L:return na[4*(A-1)+0];case ra.M:return na[4*(A-1)+1];case ra.Q:return na[4*(A-1)+2];case ra.H:return na[4*(A-1)+3];default:return}},ta.getTotalCodewordsCount=function(A,e){switch(e){case ra.L:return sa[4*(A-1)+0];case ra.M:return sa[4*(A-1)+1];case ra.Q:return sa[4*(A-1)+2];case ra.H:return sa[4*(A-1)+3];default:return}};var oa={},ia={};const aa=new Uint8Array(512),la=new Uint8Array(256);!function(){let A=1;for(let e=0;e<255;e++)aa[e]=A,la[A]=e,A<<=1,256&A&&(A^=285);for(let e=255;e<512;e++)aa[e]=aa[e-255]}(),ia.log=function(A){if(A<1)throw new Error("log("+A+")");return la[A]},ia.exp=function(A){return aa[A]},ia.mul=function(A,e){return 0===A||0===e?0:aa[la[A]+la[e]]},function(A){const e=ia;A.mul=function(A,t){const r=new Uint8Array(A.length+t.length-1);for(let n=0;n<A.length;n++)for(let s=0;s<t.length;s++)r[n+s]^=e.mul(A[n],t[s]);return r},A.mod=function(A,t){let r=new Uint8Array(A);for(;r.length-t.length>=0;){const A=r[0];for(let s=0;s<t.length;s++)r[s]^=e.mul(t[s],A);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},A.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let n=0;n<t;n++)r=A.mul(r,new Uint8Array([1,e.exp(n)]));return r}}(oa);const ca=oa;function Ba(A){this.genPoly=void 0,this.degree=A,this.degree&&this.initialize(this.degree)}Ba.prototype.initialize=function(A){this.degree=A,this.genPoly=ca.generateECPolynomial(this.degree)},Ba.prototype.encode=function(A){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(A.length+this.degree);e.set(A);const t=ca.mod(e,this.genPoly),r=this.degree-t.length;if(r>0){const A=new Uint8Array(this.degree);return A.set(t,r),A}return t};var ua=Ba,da={},ga={},wa={isValid:function(A){return!isNaN(A)&&A>=1&&A<=40}},fa={};const pa="[0-9]+";let Qa="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Qa=Qa.replace(/u/g,"\\u");const ha="(?:(?![A-Z0-9 $%*+\\-./:]|"+Qa+")(?:.|[\r\n]))+";fa.KANJI=new RegExp(Qa,"g"),fa.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),fa.BYTE=new RegExp(ha,"g"),fa.NUMERIC=new RegExp(pa,"g"),fa.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const Ca=new RegExp("^"+Qa+"$"),Ua=new RegExp("^"+pa+"$"),Fa=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");fa.testKanji=function(A){return Ca.test(A)},fa.testNumeric=function(A){return Ua.test(A)},fa.testAlphanumeric=function(A){return Fa.test(A)},function(A){const e=wa,t=fa;A.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},A.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},A.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},A.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},A.MIXED={bit:-1},A.getCharCountIndicator=function(A,t){if(!A.ccBits)throw new Error("Invalid mode: "+A);if(!e.isValid(t))throw new Error("Invalid version: "+t);return t>=1&&t<10?A.ccBits[0]:t<27?A.ccBits[1]:A.ccBits[2]},A.getBestModeForData=function(e){return t.testNumeric(e)?A.NUMERIC:t.testAlphanumeric(e)?A.ALPHANUMERIC:t.testKanji(e)?A.KANJI:A.BYTE},A.toString=function(A){if(A&&A.id)return A.id;throw new Error("Invalid mode")},A.isValid=function(A){return A&&A.bit&&A.ccBits},A.from=function(e,t){if(A.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return A.NUMERIC;case"alphanumeric":return A.ALPHANUMERIC;case"kanji":return A.KANJI;case"byte":return A.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(r){return t}}}(ga),function(A){const e=Ni,t=ta,r=Yi,n=ga,s=wa,o=e.getBCHDigit(7973);function i(A,e){return n.getCharCountIndicator(A,e)+4}function a(A,e){let t=0;return A.forEach((function(A){const r=i(A.mode,e);t+=r+A.getBitsLength()})),t}A.from=function(A,e){return s.isValid(A)?parseInt(A,10):e},A.getCapacity=function(A,r,o){if(!s.isValid(A))throw new Error("Invalid QR Code version");void 0===o&&(o=n.BYTE);const a=8*(e.getSymbolTotalCodewords(A)-t.getTotalCodewordsCount(A,r));if(o===n.MIXED)return a;const l=a-i(o,A);switch(o){case n.NUMERIC:return Math.floor(l/10*3);case n.ALPHANUMERIC:return Math.floor(l/11*2);case n.KANJI:return Math.floor(l/13);case n.BYTE:default:return Math.floor(l/8)}},A.getBestVersionForData=function(e,t){let s;const o=r.from(t,r.M);if(Array.isArray(e)){if(e.length>1)return function(e,t){for(let r=1;r<=40;r++)if(a(e,r)<=A.getCapacity(r,t,n.MIXED))return r}(e,o);if(0===e.length)return 1;s=e[0]}else s=e;return function(e,t,r){for(let n=1;n<=40;n++)if(t<=A.getCapacity(n,r,e))return n}(s.mode,s.getLength(),o)},A.getEncodedBits=function(A){if(!s.isValid(A)||A<7)throw new Error("Invalid QR Code version");let t=A<<12;for(;e.getBCHDigit(t)-o>=0;)t^=7973<<e.getBCHDigit(t)-o;return A<<12|t}}(da);var ya={};const va=Ni,ma=va.getBCHDigit(1335);ya.getEncodedBits=function(A,e){const t=A.bit<<3|e;let r=t<<10;for(;va.getBCHDigit(r)-ma>=0;)r^=1335<<va.getBCHDigit(r)-ma;return 21522^(t<<10|r)};var ba={};const Ea=ga;function Ha(A){this.mode=Ea.NUMERIC,this.data=A.toString()}Ha.getBitsLength=function(A){return 10*Math.floor(A/3)+(A%3?A%3*3+1:0)},Ha.prototype.getLength=function(){return this.data.length},Ha.prototype.getBitsLength=function(){return Ha.getBitsLength(this.data.length)},Ha.prototype.write=function(A){let e,t,r;for(e=0;e+3<=this.data.length;e+=3)t=this.data.substr(e,3),r=parseInt(t,10),A.put(r,10);const n=this.data.length-e;n>0&&(t=this.data.substr(e),r=parseInt(t,10),A.put(r,3*n+1))};var Ia=Ha;const xa=ga,Ka=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function La(A){this.mode=xa.ALPHANUMERIC,this.data=A}La.getBitsLength=function(A){return 11*Math.floor(A/2)+A%2*6},La.prototype.getLength=function(){return this.data.length},La.prototype.getBitsLength=function(){return La.getBitsLength(this.data.length)},La.prototype.write=function(A){let e;for(e=0;e+2<=this.data.length;e+=2){let t=45*Ka.indexOf(this.data[e]);t+=Ka.indexOf(this.data[e+1]),A.put(t,11)}this.data.length%2&&A.put(Ka.indexOf(this.data[e]),6)};var Da=La;const Sa=ga;function Ta(A){this.mode=Sa.BYTE,this.data="string"==typeof A?(new TextEncoder).encode(A):new Uint8Array(A)}Ta.getBitsLength=function(A){return 8*A},Ta.prototype.getLength=function(){return this.data.length},Ta.prototype.getBitsLength=function(){return Ta.getBitsLength(this.data.length)},Ta.prototype.write=function(A){for(let e=0,t=this.data.length;e<t;e++)A.put(this.data[e],8)};var Ma=Ta;const Oa=ga,ka=Ni;function _a(A){this.mode=Oa.KANJI,this.data=A}_a.getBitsLength=function(A){return 13*A},_a.prototype.getLength=function(){return this.data.length},_a.prototype.getBitsLength=function(){return _a.getBitsLength(this.data.length)},_a.prototype.write=function(A){let e;for(e=0;e<this.data.length;e++){let t=ka.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else{if(!(t>=57408&&t<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");t-=49472}t=192*(t>>>8&255)+(255&t),A.put(t,13)}};var Ra,Pa=_a,Va={exports:{}},Na=Va.exports=Ra={single_source_shortest_paths:function(A,e,t){var r={},n={};n[e]=0;var s,o,i,a,l,c,B,u=Ra.PriorityQueue.make();for(u.push(e,0);!u.empty();)for(i in o=(s=u.pop()).value,a=s.cost,l=A[o]||{})l.hasOwnProperty(i)&&(c=a+l[i],B=n[i],(void 0===n[i]||B>c)&&(n[i]=c,u.push(i,c),r[i]=o));if(void 0!==t&&void 0===n[t]){var d=["Could not find a path from ",e," to ",t,"."].join("");throw new Error(d)}return r},extract_shortest_path_from_predecessor_list:function(A,e){for(var t=[],r=e;r;)t.push(r),A[r],r=A[r];return t.reverse(),t},find_path:function(A,e,t){var r=Ra.single_source_shortest_paths(A,e,t);return Ra.extract_shortest_path_from_predecessor_list(r,t)},PriorityQueue:{make:function(A){var e,t=Ra.PriorityQueue,r={};for(e in A=A||{},t)t.hasOwnProperty(e)&&(r[e]=t[e]);return r.queue=[],r.sorter=A.sorter||t.default_sorter,r},default_sorter:function(A,e){return A.cost-e.cost},push:function(A,e){var t={value:A,cost:e};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};!function(A){const e=ga,t=Ia,r=Da,n=Ma,s=Pa,o=fa,i=Ni,a=Na;function l(A){return unescape(encodeURIComponent(A)).length}function c(A,e,t){const r=[];let n;for(;null!==(n=A.exec(t));)r.push({data:n[0],index:n.index,mode:e,length:n[0].length});return r}function B(A){const t=c(o.NUMERIC,e.NUMERIC,A),r=c(o.ALPHANUMERIC,e.ALPHANUMERIC,A);let n,s;i.isKanjiModeEnabled()?(n=c(o.BYTE,e.BYTE,A),s=c(o.KANJI,e.KANJI,A)):(n=c(o.BYTE_KANJI,e.BYTE,A),s=[]);return t.concat(r,n,s).sort((function(A,e){return A.index-e.index})).map((function(A){return{data:A.data,mode:A.mode,length:A.length}}))}function u(A,o){switch(o){case e.NUMERIC:return t.getBitsLength(A);case e.ALPHANUMERIC:return r.getBitsLength(A);case e.KANJI:return s.getBitsLength(A);case e.BYTE:return n.getBitsLength(A)}}function d(A,o){let a;const l=e.getBestModeForData(A);if(a=e.from(o,l),a!==e.BYTE&&a.bit<l.bit)throw new Error('"'+A+'" cannot be encoded with mode '+e.toString(a)+".\n Suggested mode is: "+e.toString(l));switch(a!==e.KANJI||i.isKanjiModeEnabled()||(a=e.BYTE),a){case e.NUMERIC:return new t(A);case e.ALPHANUMERIC:return new r(A);case e.KANJI:return new s(A);case e.BYTE:return new n(A)}}A.fromArray=function(A){return A.reduce((function(A,e){return"string"==typeof e?A.push(d(e,null)):e.data&&A.push(d(e.data,e.mode)),A}),[])},A.fromString=function(t,r){const n=function(A){const t=[];for(let r=0;r<A.length;r++){const n=A[r];switch(n.mode){case e.NUMERIC:t.push([n,{data:n.data,mode:e.ALPHANUMERIC,length:n.length},{data:n.data,mode:e.BYTE,length:n.length}]);break;case e.ALPHANUMERIC:t.push([n,{data:n.data,mode:e.BYTE,length:n.length}]);break;case e.KANJI:t.push([n,{data:n.data,mode:e.BYTE,length:l(n.data)}]);break;case e.BYTE:t.push([{data:n.data,mode:e.BYTE,length:l(n.data)}])}}return t}(B(t,i.isKanjiModeEnabled())),s=function(A,t){const r={},n={start:{}};let s=["start"];for(let o=0;o<A.length;o++){const i=A[o],a=[];for(let A=0;A<i.length;A++){const l=i[A],c=""+o+A;a.push(c),r[c]={node:l,lastCount:0},n[c]={};for(let A=0;A<s.length;A++){const o=s[A];r[o]&&r[o].node.mode===l.mode?(n[o][c]=u(r[o].lastCount+l.length,l.mode)-u(r[o].lastCount,l.mode),r[o].lastCount+=l.length):(r[o]&&(r[o].lastCount=l.length),n[o][c]=u(l.length,l.mode)+4+e.getCharCountIndicator(l.mode,t))}}s=a}for(let e=0;e<s.length;e++)n[s[e]].end=0;return{map:n,table:r}}(n,r),o=a.find_path(s.map,"start","end"),c=[];for(let A=1;A<o.length-1;A++)c.push(s.table[o[A]].node);return A.fromArray(function(A){return A.reduce((function(A,e){const t=A.length-1>=0?A[A.length-1]:null;return t&&t.mode===e.mode?(A[A.length-1].data+=e.data,A):(A.push(e),A)}),[])}(c))},A.rawSplit=function(e){return A.fromArray(B(e,i.isKanjiModeEnabled()))}}(ba);const Ga=Ni,Ja=Yi,Xa=Zi,Ya=zi,Wa=$i,Za=qi,ja=ea,za=ta,$a=ua,qa=da,Al=ya,el=ga,tl=ba;function rl(A,e,t){const r=A.size,n=Al.getEncodedBits(e,t);let s,o;for(s=0;s<15;s++)o=1==(n>>s&1),s<6?A.set(s,8,o,!0):s<8?A.set(s+1,8,o,!0):A.set(r-15+s,8,o,!0),s<8?A.set(8,r-s-1,o,!0):s<9?A.set(8,15-s-1+1,o,!0):A.set(8,15-s-1,o,!0);A.set(r-8,8,1,!0)}function nl(A,e,t){const r=new Xa;t.forEach((function(e){r.put(e.mode.bit,4),r.put(e.getLength(),el.getCharCountIndicator(e.mode,A)),e.write(r)}));const n=8*(Ga.getSymbolTotalCodewords(A)-za.getTotalCodewordsCount(A,e));for(r.getLengthInBits()+4<=n&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);const s=(n-r.getLengthInBits())/8;for(let o=0;o<s;o++)r.put(o%2?17:236,8);return function(A,e,t){const r=Ga.getSymbolTotalCodewords(e),n=za.getTotalCodewordsCount(e,t),s=r-n,o=za.getBlocksCount(e,t),i=r%o,a=o-i,l=Math.floor(r/o),c=Math.floor(s/o),B=c+1,u=l-c,d=new $a(u);let g=0;const w=new Array(o),f=new Array(o);let p=0;const Q=new Uint8Array(A.buffer);for(let y=0;y<o;y++){const A=y<a?c:B;w[y]=Q.slice(g,g+A),f[y]=d.encode(w[y]),g+=A,p=Math.max(p,A)}const h=new Uint8Array(r);let C,U,F=0;for(C=0;C<p;C++)for(U=0;U<o;U++)C<w[U].length&&(h[F++]=w[U][C]);for(C=0;C<u;C++)for(U=0;U<o;U++)h[F++]=f[U][C];return h}(r,A,e)}function sl(A,e,t,r){let n;if(Array.isArray(A))n=tl.fromArray(A);else{if("string"!=typeof A)throw new Error("Invalid data");{let r=e;if(!r){const e=tl.rawSplit(A);r=qa.getBestVersionForData(e,t)}n=tl.fromString(A,r||40)}}const s=qa.getBestVersionForData(n,t);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<s)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+s+".\n")}else e=s;const o=nl(e,t,n),i=Ga.getSymbolSize(e),a=new Ya(i);return function(A,e){const t=A.size,r=Za.getPositions(e);for(let n=0;n<r.length;n++){const e=r[n][0],s=r[n][1];for(let r=-1;r<=7;r++)if(!(e+r<=-1||t<=e+r))for(let n=-1;n<=7;n++)s+n<=-1||t<=s+n||(r>=0&&r<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===r||6===r)||r>=2&&r<=4&&n>=2&&n<=4?A.set(e+r,s+n,!0,!0):A.set(e+r,s+n,!1,!0))}}(a,e),function(A){const e=A.size;for(let t=8;t<e-8;t++){const e=t%2==0;A.set(t,6,e,!0),A.set(6,t,e,!0)}}(a),function(A,e){const t=Wa.getPositions(e);for(let r=0;r<t.length;r++){const e=t[r][0],n=t[r][1];for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)-2===t||2===t||-2===r||2===r||0===t&&0===r?A.set(e+t,n+r,!0,!0):A.set(e+t,n+r,!1,!0)}}(a,e),rl(a,t,0),e>=7&&function(A,e){const t=A.size,r=qa.getEncodedBits(e);let n,s,o;for(let i=0;i<18;i++)n=Math.floor(i/3),s=i%3+t-8-3,o=1==(r>>i&1),A.set(n,s,o,!0),A.set(s,n,o,!0)}(a,e),function(A,e){const t=A.size;let r=-1,n=t-1,s=7,o=0;for(let i=t-1;i>0;i-=2)for(6===i&&i--;;){for(let t=0;t<2;t++)if(!A.isReserved(n,i-t)){let r=!1;o<e.length&&(r=1==(e[o]>>>s&1)),A.set(n,i-t,r),s--,-1===s&&(o++,s=7)}if(n+=r,n<0||t<=n){n-=r,r=-r;break}}}(a,o),isNaN(r)&&(r=ja.getBestMask(a,rl.bind(null,a,t))),ja.applyMask(r,a),rl(a,t,r),{modules:a,version:e,errorCorrectionLevel:t,maskPattern:r,segments:n}}Vi.create=function(A,e){if(void 0===A||""===A)throw new Error("No input text");let t,r,n=Ja.M;return void 0!==e&&(n=Ja.from(e.errorCorrectionLevel,Ja.M),t=qa.from(e.version),r=ja.from(e.maskPattern),e.toSJISFunc&&Ga.setToSJISFunction(e.toSJISFunc)),sl(A,t,n,r)};var ol={},il={};!function(A){function e(A){if("number"==typeof A&&(A=A.toString()),"string"!=typeof A)throw new Error("Color should be defined as hex string");let e=A.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+A);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(A){return[A,A]})))),6===e.length&&e.push("F","F");const t=parseInt(e.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:255&t,hex:"#"+e.slice(0,6).join("")}}A.getOptions=function(A){A||(A={}),A.color||(A.color={});const t=void 0===A.margin||null===A.margin||A.margin<0?4:A.margin,r=A.width&&A.width>=21?A.width:void 0,n=A.scale||4;return{width:r,scale:r?4:n,margin:t,color:{dark:e(A.color.dark||"#000000ff"),light:e(A.color.light||"#ffffffff")},type:A.type,rendererOpts:A.rendererOpts||{}}},A.getScale=function(A,e){return e.width&&e.width>=A+2*e.margin?e.width/(A+2*e.margin):e.scale},A.getImageWidth=function(e,t){const r=A.getScale(e,t);return Math.floor((e+2*t.margin)*r)},A.qrToImageData=function(e,t,r){const n=t.modules.size,s=t.modules.data,o=A.getScale(n,r),i=Math.floor((n+2*r.margin)*o),a=r.margin*o,l=[r.color.light,r.color.dark];for(let A=0;A<i;A++)for(let t=0;t<i;t++){let c=4*(A*i+t),B=r.color.light;if(A>=a&&t>=a&&A<i-a&&t<i-a){B=l[s[Math.floor((A-a)/o)*n+Math.floor((t-a)/o)]?1:0]}e[c++]=B.r,e[c++]=B.g,e[c++]=B.b,e[c]=B.a}}}(il),function(A){const e=il;A.render=function(A,t,r){let n=r,s=t;void 0!==n||t&&t.getContext||(n=t,t=void 0),t||(s=function(){try{return document.createElement("canvas")}catch(A){throw new Error("You need to specify a canvas element")}}()),n=e.getOptions(n);const o=e.getImageWidth(A.modules.size,n),i=s.getContext("2d"),a=i.createImageData(o,o);return e.qrToImageData(a.data,A,n),function(A,e,t){A.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=t,e.width=t,e.style.height=t+"px",e.style.width=t+"px"}(i,s,o),i.putImageData(a,0,0),s},A.renderToDataURL=function(e,t,r){let n=r;void 0!==n||t&&t.getContext||(n=t,t=void 0),n||(n={});const s=A.render(e,t,n),o=n.type||"image/png",i=n.rendererOpts||{};return s.toDataURL(o,i.quality)}}(ol);var al={};const ll=il;function cl(A,e){const t=A.a/255,r=e+'="'+A.hex+'"';return t<1?r+" "+e+'-opacity="'+t.toFixed(2).slice(1)+'"':r}function Bl(A,e,t){let r=A+e;return void 0!==t&&(r+=" "+t),r}al.render=function(A,e,t){const r=ll.getOptions(e),n=A.modules.size,s=A.modules.data,o=n+2*r.margin,i=r.color.light.a?"<path "+cl(r.color.light,"fill")+' d="M0 0h'+o+"v"+o+'H0z"/>':"",a="<path "+cl(r.color.dark,"stroke")+' d="'+function(A,e,t){let r="",n=0,s=!1,o=0;for(let i=0;i<A.length;i++){const a=Math.floor(i%e),l=Math.floor(i/e);a||s||(s=!0),A[i]?(o++,i>0&&a>0&&A[i-1]||(r+=s?Bl("M",a+t,.5+l+t):Bl("m",n,0),n=0,s=!1),a+1<e&&A[i+1]||(r+=Bl("h",o),o=0)):n++}return r}(s,n,r.margin)+'"/>',l='viewBox="0 0 '+o+" "+o+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+l+' shape-rendering="crispEdges">'+i+a+"</svg>\n";return"function"==typeof t&&t(null,c),c};const ul=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},dl=Vi,gl=ol,wl=al;function fl(A,e,t,r,n){const s=[].slice.call(arguments,1),o=s.length,i="function"==typeof s[o-1];if(!i&&!ul())throw new Error("Callback required as last argument");if(!i){if(o<1)throw new Error("Too few arguments provided");return 1===o?(t=e,e=r=void 0):2!==o||e.getContext||(r=t,t=e,e=void 0),new Promise((function(n,s){try{const s=dl.create(t,r);n(A(s,e,r))}catch(o){s(o)}}))}if(o<2)throw new Error("Too few arguments provided");2===o?(n=t,t=e,e=r=void 0):3===o&&(e.getContext&&void 0===n?(n=r,r=void 0):(n=r,r=t,t=e,e=void 0));try{const s=dl.create(t,r);n(null,A(s,e,r))}catch(a){n(a)}}Pi.create=dl.create,Pi.toCanvas=fl.bind(null,gl.render),Pi.toDataURL=fl.bind(null,gl.renderToDataURL),Pi.toString=fl.bind(null,(function(A,e,t){return wl.render(A,t)}));const pl=Symbol(),Ql="el",hl=(A,e,t,r,n)=>{let s=`${A}-${e}`;return t&&(s+=`-${t}`),r&&(s+=`__${r}`),n&&(s+=`--${n}`),s},Cl=Symbol("namespaceContextKey"),Ul=A=>{const o=A||(e()?t(Cl,r(Ql)):r(Ql));return n((()=>s(o)||Ql))},Fl=(A,e)=>{const t=Ul(e);return{namespace:t,b:(e="")=>hl(t.value,A,e,"",""),e:e=>e?hl(t.value,A,"",e,""):"",m:e=>e?hl(t.value,A,"","",e):"",be:(e,r)=>e&&r?hl(t.value,A,e,r,""):"",em:(e,r)=>e&&r?hl(t.value,A,"",e,r):"",bm:(e,r)=>e&&r?hl(t.value,A,e,"",r):"",bem:(e,r,n)=>e&&r&&n?hl(t.value,A,e,r,n):"",is:(A,...e)=>{const t=!(e.length>=1)||e[0];return A&&t?`is-${A}`:""},cssVar:A=>{const e={};for(const r in A)A[r]&&(e[`--${t.value}-${r}`]=A[r]);return e},cssVarName:A=>`--${t.value}-${A}`,cssVarBlock:e=>{const r={};for(const n in e)e[n]&&(r[`--${t.value}-${A}-${n}`]=e[n]);return r},cssVarBlockName:e=>`--${t.value}-${A}-${e}`}};var yl="object"==typeof global&&global&&global.Object===Object&&global,vl="object"==typeof self&&self&&self.Object===Object&&self,ml=yl||vl||Function("return this")(),bl=ml.Symbol,El=Object.prototype,Hl=El.hasOwnProperty,Il=El.toString,xl=bl?bl.toStringTag:void 0;var Kl=Object.prototype.toString;var Ll=bl?bl.toStringTag:void 0;function Dl(A){return null==A?void 0===A?"[object Undefined]":"[object Null]":Ll&&Ll in Object(A)?function(A){var e=Hl.call(A,xl),t=A[xl];try{A[xl]=void 0;var r=!0}catch(s){}var n=Il.call(A);return r&&(e?A[xl]=t:delete A[xl]),n}(A):function(A){return Kl.call(A)}(A)}function Sl(A){return null!=A&&"object"==typeof A}function Tl(A){return"symbol"==typeof A||Sl(A)&&"[object Symbol]"==Dl(A)}var Ml=Array.isArray,Ol=bl?bl.prototype:void 0,kl=Ol?Ol.toString:void 0;function _l(A){if("string"==typeof A)return A;if(Ml(A))return function(A,e){for(var t=-1,r=null==A?0:A.length,n=Array(r);++t<r;)n[t]=e(A[t],t,A);return n}(A,_l)+"";if(Tl(A))return kl?kl.call(A):"";var e=A+"";return"0"==e&&1/A==-1/0?"-0":e}function Rl(A){var e=typeof A;return null!=A&&("object"==e||"function"==e)}var Pl,Vl=ml["__core-js_shared__"],Nl=(Pl=/[^.]+$/.exec(Vl&&Vl.keys&&Vl.keys.IE_PROTO||""))?"Symbol(src)_1."+Pl:"";var Gl=Function.prototype.toString;var Jl=/^\[object .+?Constructor\]$/,Xl=Function.prototype,Yl=Object.prototype,Wl=Xl.toString,Zl=Yl.hasOwnProperty,jl=RegExp("^"+Wl.call(Zl).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function zl(A){if(!Rl(A)||(e=A,Nl&&Nl in e))return!1;var e,t=function(A){if(!Rl(A))return!1;var e=Dl(A);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}(A)?jl:Jl;return t.test(function(A){if(null!=A){try{return Gl.call(A)}catch(e){}try{return A+""}catch(e){}}return""}(A))}function $l(A,e){var t=function(A,e){return null==A?void 0:A[e]}(A,e);return zl(t)?t:void 0}var ql=Date.now;var Ac,ec,tc,rc=function(){try{var A=$l(Object,"defineProperty");return A({},"",{}),A}catch(e){}}(),nc=rc?function(A,e){return rc(A,"toString",{configurable:!0,enumerable:!1,value:(t=e,function(){return t}),writable:!0});var t}:function(A){return A},sc=(Ac=nc,ec=0,tc=0,function(){var A=ql(),e=16-(A-tc);if(tc=A,e>0){if(++ec>=800)return arguments[0]}else ec=0;return Ac.apply(void 0,arguments)}),oc=/^(?:0|[1-9]\d*)$/;function ic(A,e){var t=typeof A;return!!(e=null==e?9007199254740991:e)&&("number"==t||"symbol"!=t&&oc.test(A))&&A>-1&&A%1==0&&A<e}function ac(A,e){return A===e||A!=A&&e!=e}var lc=Object.prototype.hasOwnProperty;function cc(A,e,t){var r=A[e];lc.call(A,e)&&ac(r,t)&&(void 0!==t||e in A)||function(A,e,t){"__proto__"==e&&rc?rc(A,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):A[e]=t}(A,e,t)}var Bc=Math.max;function uc(A){return Sl(A)&&"[object Arguments]"==Dl(A)}var dc=Object.prototype,gc=dc.hasOwnProperty,wc=dc.propertyIsEnumerable,fc=uc(function(){return arguments}())?uc:function(A){return Sl(A)&&gc.call(A,"callee")&&!wc.call(A,"callee")},pc=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Qc=/^\w*$/;var hc=$l(Object,"create");var Cc=Object.prototype.hasOwnProperty;var Uc=Object.prototype.hasOwnProperty;function Fc(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}function yc(A,e){for(var t=A.length;t--;)if(ac(A[t][0],e))return t;return-1}Fc.prototype.clear=function(){this.__data__=hc?hc(null):{},this.size=0},Fc.prototype.delete=function(A){var e=this.has(A)&&delete this.__data__[A];return this.size-=e?1:0,e},Fc.prototype.get=function(A){var e=this.__data__;if(hc){var t=e[A];return"__lodash_hash_undefined__"===t?void 0:t}return Cc.call(e,A)?e[A]:void 0},Fc.prototype.has=function(A){var e=this.__data__;return hc?void 0!==e[A]:Uc.call(e,A)},Fc.prototype.set=function(A,e){var t=this.__data__;return this.size+=this.has(A)?0:1,t[A]=hc&&void 0===e?"__lodash_hash_undefined__":e,this};var vc=Array.prototype.splice;function mc(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}mc.prototype.clear=function(){this.__data__=[],this.size=0},mc.prototype.delete=function(A){var e=this.__data__,t=yc(e,A);return!(t<0)&&(t==e.length-1?e.pop():vc.call(e,t,1),--this.size,!0)},mc.prototype.get=function(A){var e=this.__data__,t=yc(e,A);return t<0?void 0:e[t][1]},mc.prototype.has=function(A){return yc(this.__data__,A)>-1},mc.prototype.set=function(A,e){var t=this.__data__,r=yc(t,A);return r<0?(++this.size,t.push([A,e])):t[r][1]=e,this};var bc=$l(ml,"Map");function Ec(A,e){var t,r,n=A.__data__;return("string"==(r=typeof(t=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==t:null===t)?n["string"==typeof e?"string":"hash"]:n.map}function Hc(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}Hc.prototype.clear=function(){this.size=0,this.__data__={hash:new Fc,map:new(bc||mc),string:new Fc}},Hc.prototype.delete=function(A){var e=Ec(this,A).delete(A);return this.size-=e?1:0,e},Hc.prototype.get=function(A){return Ec(this,A).get(A)},Hc.prototype.has=function(A){return Ec(this,A).has(A)},Hc.prototype.set=function(A,e){var t=Ec(this,A),r=t.size;return t.set(A,e),this.size+=t.size==r?0:1,this};function Ic(A,e){if("function"!=typeof A||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var t=function(){var r=arguments,n=e?e.apply(this,r):r[0],s=t.cache;if(s.has(n))return s.get(n);var o=A.apply(this,r);return t.cache=s.set(n,o)||s,o};return t.cache=new(Ic.Cache||Hc),t}Ic.Cache=Hc;var xc=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Kc=/\\(\\)?/g,Lc=function(A){var e=Ic(A,(function(A){return 500===t.size&&t.clear(),A})),t=e.cache;return e}((function(A){var e=[];return 46===A.charCodeAt(0)&&e.push(""),A.replace(xc,(function(A,t,r,n){e.push(r?n.replace(Kc,"$1"):t||A)})),e}));function Dc(A,e){return Ml(A)?A:function(A,e){if(Ml(A))return!1;var t=typeof A;return!("number"!=t&&"symbol"!=t&&"boolean"!=t&&null!=A&&!Tl(A))||Qc.test(A)||!pc.test(A)||null!=e&&A in Object(e)}(A,e)?[A]:Lc(function(A){return null==A?"":_l(A)}(A))}function Sc(A){if("string"==typeof A||Tl(A))return A;var e=A+"";return"0"==e&&1/A==-1/0?"-0":e}function Tc(A,e){for(var t=0,r=(e=Dc(e,A)).length;null!=A&&t<r;)A=A[Sc(e[t++])];return t&&t==r?A:void 0}function Mc(A,e){for(var t=-1,r=e.length,n=A.length;++t<r;)A[n+t]=e[t];return A}var Oc=bl?bl.isConcatSpreadable:void 0;function kc(A){return Ml(A)||fc(A)||!!(Oc&&A&&A[Oc])}function _c(A){return(null==A?0:A.length)?function(A,e,t,r,n){var s=-1,o=A.length;for(t||(t=kc),n||(n=[]);++s<o;){var i=A[s];t(i)?Mc(n,i):n[n.length]=i}return n}(A):[]}function Rc(A,e){return null!=A&&e in Object(A)}function Pc(A,e){return null!=A&&function(A,e,t){for(var r,n=-1,s=(e=Dc(e,A)).length,o=!1;++n<s;){var i=Sc(e[n]);if(!(o=null!=A&&t(A,i)))break;A=A[i]}return o||++n!=s?o:!!(s=null==A?0:A.length)&&"number"==typeof(r=s)&&r>-1&&r%1==0&&r<=9007199254740991&&ic(i,s)&&(Ml(A)||fc(A))}(A,e,Rc)}function Vc(A){for(var e=-1,t=null==A?0:A.length,r={};++e<t;){var n=A[e];r[n[0]]=n[1]}return r}function Nc(A){return null==A}function Gc(A,e,t,r){if(!Rl(A))return A;for(var n=-1,s=(e=Dc(e,A)).length,o=s-1,i=A;null!=i&&++n<s;){var a=Sc(e[n]),l=t;if("__proto__"===a||"constructor"===a||"prototype"===a)return A;if(n!=o){var c=i[a];void 0===(l=void 0)&&(l=Rl(c)?c:ic(e[n+1])?[]:{})}cc(i,a,l),i=i[a]}return A}function Jc(A,e){return function(A,e,t){for(var r=-1,n=e.length,s={};++r<n;){var o=e[r],i=Tc(A,o);t(i,o)&&Gc(s,Dc(o,A),i)}return s}(A,e,(function(e,t){return Pc(A,t)}))}var Xc=function(A){return sc(function(A,e,t){return e=Bc(void 0===e?A.length-1:e,0),function(){for(var r=arguments,n=-1,s=Bc(r.length-e,0),o=Array(s);++n<s;)o[n]=r[e+n];n=-1;for(var i=Array(e+1);++n<e;)i[n]=r[n];return i[e]=t(o),function(A,e,t){switch(t.length){case 0:return A.call(e);case 1:return A.call(e,t[0]);case 2:return A.call(e,t[0],t[1]);case 3:return A.call(e,t[0],t[1],t[2])}return A.apply(e,t)}(A,this,i)}}(A,void 0,_c),A+"")}((function(A,e){return null==A?{}:Jc(A,e)}));const Yc=A=>"boolean"==typeof A,Wc=A=>"number"==typeof A,Zc=A=>"undefined"!=typeof Element&&A instanceof Element;var jc,zc=Object.defineProperty,$c=Object.defineProperties,qc=Object.getOwnPropertyDescriptors,AB=Object.getOwnPropertySymbols,eB=Object.prototype.hasOwnProperty,tB=Object.prototype.propertyIsEnumerable,rB=(A,e,t)=>e in A?zc(A,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):A[e]=t;function nB(A,e){const t=i();var r,n;return a((()=>{t.value=A()}),(r=((A,e)=>{for(var t in e||(e={}))eB.call(e,t)&&rB(A,t,e[t]);if(AB)for(var t of AB(e))tB.call(e,t)&&rB(A,t,e[t]);return A})({},e),n={flush:null!=void 0?void 0:"sync"},$c(r,qc(n)))),l(t)}const sB="undefined"!=typeof window,oB=()=>{},iB=sB&&(null==(jc=null==window?void 0:window.navigator)?void 0:jc.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function aB(A){return"function"==typeof A?A():s(A)}function lB(A){return!!c()&&(B(A),!0)}function cB(A){var e;const t=aB(A);return null!=(e=null==t?void 0:t.$el)?e:t}const BB=sB?window:void 0;function uB(...A){let e,t,r,n;if("string"==typeof A[0]||Array.isArray(A[0])?([t,r,n]=A,e=BB):[e,t,r,n]=A,!e)return oB;Array.isArray(t)||(t=[t]),Array.isArray(r)||(r=[r]);const s=[],o=()=>{s.forEach((A=>A())),s.length=0},i=g((()=>[cB(e),aB(n)]),(([A,e])=>{o(),A&&s.push(...t.flatMap((t=>r.map((r=>((A,e,t,r)=>(A.addEventListener(e,t,r),()=>A.removeEventListener(e,t,r)))(A,t,r,e))))))}),{immediate:!0,flush:"post"}),a=()=>{i(),o()};return lB(a),a}let dB=!1;function gB(A,t=!1){const n=r(),s=()=>n.value=Boolean(A());return s(),function(A,t=!0){e()?u(A):t?A():d(A)}(s,t),n}const wB="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},fB="__vueuse_ssr_handlers__";wB[fB]=wB[fB]||{};var pB,QB,hB=Object.getOwnPropertySymbols,CB=Object.prototype.hasOwnProperty,UB=Object.prototype.propertyIsEnumerable;function FB(A,e,t={}){const r=t,{window:n=BB}=r,s=((A,e)=>{var t={};for(var r in A)CB.call(A,r)&&e.indexOf(r)<0&&(t[r]=A[r]);if(null!=A&&hB)for(var r of hB(A))e.indexOf(r)<0&&UB.call(A,r)&&(t[r]=A[r]);return t})(r,["window"]);let o;const i=gB((()=>n&&"ResizeObserver"in n)),a=()=>{o&&(o.disconnect(),o=void 0)},l=g((()=>cB(A)),(A=>{a(),i.value&&n&&A&&(o=new ResizeObserver(e),o.observe(A,s))}),{immediate:!0,flush:"post"}),c=()=>{a(),l()};return lB(c),{isSupported:i,stop:c}}(QB=pB||(pB={})).UP="UP",QB.RIGHT="RIGHT",QB.DOWN="DOWN",QB.LEFT="LEFT",QB.NONE="NONE";var yB=Object.defineProperty,vB=Object.getOwnPropertySymbols,mB=Object.prototype.hasOwnProperty,bB=Object.prototype.propertyIsEnumerable,EB=(A,e,t)=>e in A?yB(A,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):A[e]=t;((A,e)=>{for(var t in e||(e={}))mB.call(e,t)&&EB(A,t,e[t]);if(vB)for(var t of vB(e))bB.call(e,t)&&EB(A,t,e[t])})({linear:function(A){return A}},{easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]});const HB={current:0},IB=r(0),xB=Symbol("elZIndexContextKey"),KB=Symbol("zIndexContextKey"),LB=A=>{const r=e()?t(xB,HB):HB,o=A||(e()?t(KB,void 0):void 0),i=n((()=>{const A=s(o);return Wc(A)?A:2e3})),a=n((()=>i.value+IB.value));return!sB&&t(xB),{initialZIndex:i,currentZIndex:a,nextZIndex:()=>(r.current++,IB.value=r.current,a.value)}};var DB={name:"en",el:{breadcrumb:{label:"Breadcrumb"},colorpicker:{confirm:"OK",clear:"Clear",defaultLabel:"color picker",description:"current color is {color}. press enter to select a new color.",alphaLabel:"pick alpha value"},datepicker:{now:"Now",today:"Today",cancel:"Cancel",clear:"Clear",confirm:"OK",dateTablePrompt:"Use the arrow keys and enter to select the day of the month",monthTablePrompt:"Use the arrow keys and enter to select the month",yearTablePrompt:"Use the arrow keys and enter to select the year",selectedDate:"Selected date",selectDate:"Select date",selectTime:"Select time",startDate:"Start Date",startTime:"Start Time",endDate:"End Date",endTime:"End Time",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",year:"",month1:"January",month2:"February",month3:"March",month4:"April",month5:"May",month6:"June",month7:"July",month8:"August",month9:"September",month10:"October",month11:"November",month12:"December",week:"week",weeks:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},weeksFull:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"},months:{jan:"Jan",feb:"Feb",mar:"Mar",apr:"Apr",may:"May",jun:"Jun",jul:"Jul",aug:"Aug",sep:"Sep",oct:"Oct",nov:"Nov",dec:"Dec"}},inputNumber:{decrease:"decrease number",increase:"increase number"},select:{loading:"Loading",noMatch:"No matching data",noData:"No data",placeholder:"Select"},mention:{loading:"Loading"},dropdown:{toggleDropdown:"Toggle Dropdown"},cascader:{noMatch:"No matching data",loading:"Loading",placeholder:"Select",noData:"No data"},pagination:{goto:"Go to",pagesize:"/page",total:"Total {total}",pageClassifier:"",page:"Page",prev:"Go to previous page",next:"Go to next page",currentPage:"page {pager}",prevPages:"Previous {pager} pages",nextPages:"Next {pager} pages",deprecationWarning:"Deprecated usages detected, please refer to the el-pagination documentation for more details"},dialog:{close:"Close this dialog"},drawer:{close:"Close this dialog"},messagebox:{title:"Message",confirm:"OK",cancel:"Cancel",error:"Illegal input",close:"Close this dialog"},upload:{deleteTip:"press delete to remove",delete:"Delete",preview:"Preview",continue:"Continue"},slider:{defaultLabel:"slider between {min} and {max}",defaultRangeStartLabel:"pick start value",defaultRangeEndLabel:"pick end value"},table:{emptyText:"No Data",confirmFilter:"Confirm",resetFilter:"Reset",clearFilter:"All",sumText:"Sum"},tour:{next:"Next",previous:"Previous",finish:"Finish"},tree:{emptyText:"No Data"},transfer:{noMatch:"No matching data",noData:"No data",titles:["List 1","List 2"],filterPlaceholder:"Enter keyword",noCheckedFormat:"{total} items",hasCheckedFormat:"{checked}/{total} checked"},image:{error:"FAILED"},pageHeader:{title:"Back"},popconfirm:{confirmButtonText:"Yes",cancelButtonText:"No"},carousel:{leftArrow:"Carousel arrow left",rightArrow:"Carousel arrow right",indicator:"Carousel switch to index {index}"}}};const SB=A=>(e,t)=>TB(e,t,s(A)),TB=(A,e,t)=>function(A,e,t){var r=null==A?void 0:Tc(A,e);return void 0===r?t:r}(t,A,A).replace(/\{(\w+)\}/g,((A,t)=>{var r;return`${null!=(r=null==e?void 0:e[t])?r:`{${t}}`}`})),MB=Symbol("localeContextKey"),OB=A=>{const e=A||t(MB,r());return(A=>({lang:n((()=>s(A).name)),locale:w(A)?A:r(A),t:SB(A)}))(n((()=>e.value||DB)))},kB="__epPropKey",_B=(A,e)=>{if(!f(A)||f(t=A)&&t[kB])return A;var t;const{values:r,required:n,default:s,type:o,validator:i}=A,a=r||i?t=>{let n=!1,o=[];if(r&&(o=Array.from(r),p(A,"default")&&o.push(s),n||(n=o.includes(t))),i&&(n||(n=i(t))),!n&&o.length>0){const A=[...new Set(o)].map((A=>JSON.stringify(A))).join(", ");Q(`Invalid prop: validation failed${e?` for prop "${e}"`:""}. Expected one of [${A}], got value ${JSON.stringify(t)}.`)}return n}:void 0,l={type:o,required:!!n,validator:a,[kB]:!0};return p(A,"default")&&(l.default=s),l},RB=A=>Vc(Object.entries(A).map((([A,e])=>[A,_B(e,A)]))),PB=(_B({type:String,values:["","default","small","large"],required:!1}),Symbol("size")),VB=Symbol("emptyValuesContextKey"),NB=(RB({emptyValues:Array,valueOnClear:{type:[String,Number,Boolean,Function],default:void 0,validator:A=>h(A)?!A():!A}}),A=>Object.keys(A)),GB=r();function JB(A,r=void 0){return e()?t(pl,GB):GB}const XB=(A,t,r=!1)=>{const o=!!e(),i=o?JB():void 0,a=null!=void 0?undefined:o?C:void 0;if(!a)return;const l=n((()=>{const e=s(A);return(null==i?void 0:i.value)?YB(i.value,e):e}));return a(pl,l),a(MB,n((()=>l.value.locale))),a(Cl,n((()=>l.value.namespace))),a(KB,n((()=>l.value.zIndex))),a(PB,{size:n((()=>l.value.size||""))}),a(VB,n((()=>({emptyValues:l.value.emptyValues,valueOnClear:l.value.valueOnClear})))),!r&&GB.value||(GB.value=l.value),l},YB=(A,e)=>{const t=[...new Set([...NB(A),...NB(e)])],r={};for(const n of t)r[n]=void 0!==e[n]?e[n]:A[n];return r};var WB=(A,e)=>{const t=A.__vccOpts||A;for(const[r,n]of e)t[r]=n;return t};function ZB(A,e="px"){return A?Wc(A)||o(t=A)&&!Number.isNaN(Number(t))?`${A}${e}`:o(A)?A:void 0:"";var t}const jB=(A,e)=>(A.install=e=>{for(const t of[A,...Object.values({})])e.component(t.name,t)},A),zB=RB({size:{type:[Number,String]},color:{type:String}}),$B=U({name:"ElIcon",inheritAttrs:!1});const qB=jB(WB(U({...$B,props:zB,setup(A){const e=A,t=Fl("icon"),r=n((()=>{const{size:A,color:t}=e;return A||t?{fontSize:(r=A,void 0===r?void 0:ZB(A)),"--color":t}:{};var r}));return(A,e)=>(y(),F("i",m({class:s(t).b(),style:s(r)},A.$attrs),[v(A.$slots,"default")],16))}}),[["__file","icon.vue"]]));
/*! Element Plus Icons Vue v2.3.1 */var Au=U({name:"CircleCloseFilled",__name:"circle-close-filled",setup:A=>(A,e)=>(y(),F("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336z"})]))}),eu=U({name:"Close",__name:"close",setup:A=>(A,e)=>(y(),F("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b("path",{fill:"currentColor",d:"M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"})]))}),tu=U({name:"InfoFilled",__name:"info-filled",setup:A=>(A,e)=>(y(),F("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64m67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344M590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.992 12.992 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296-44.096 0-108.992 44.736-148.48 101.504 0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04 67.84 0 107.904-43.648 147.456-100.416z"})]))});const ru=[String,Object,Function],nu={Close:eu},su={success:U({name:"SuccessFilled",__name:"success-filled",setup:A=>(A,e)=>(y(),F("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z"})]))}),warning:U({name:"WarningFilled",__name:"warning-filled",setup:A=>(A,e)=>(y(),F("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256m0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4"})]))}),error:Au,info:tu},ou=RB({ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical","undefined"]},ariaControls:String}),iu=A=>Xc(ou,A),au=Symbol("formItemContextKey"),lu={prefix:Math.floor(1e4*Math.random()),current:0},cu=Symbol("elIdInjection"),Bu=()=>e()?t(cu,lu):lu,uu=Symbol("popper"),du=Symbol("popperContent"),gu=RB({role:{type:String,values:["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],default:"tooltip"}}),wu=U({name:"ElPopper",inheritAttrs:!1});var fu=WB(U({...wu,props:gu,setup(A,{expose:e}){const t=A,s={triggerRef:r(),popperInstanceRef:r(),contentRef:r(),referenceRef:r(),role:n((()=>t.role))};return e(s),C(uu,s),(A,e)=>v(A.$slots,"default")}}),[["__file","popper.vue"]]);const pu=RB({arrowOffset:{type:Number,default:5}}),Qu=U({name:"ElPopperArrow",inheritAttrs:!1});var hu=WB(U({...Qu,props:pu,setup(A,{expose:e}){const r=A,n=Fl("popper"),{arrowOffset:o,arrowRef:i,arrowStyle:a}=t(du,void 0);return g((()=>r.arrowOffset),(A=>{o.value=A})),E((()=>{i.value=void 0})),e({arrowRef:i}),(A,e)=>(y(),F("span",{ref_key:"arrowRef",ref:i,class:I(s(n).e("arrow")),style:H(s(a)),"data-popper-arrow":""},null,6))}}),[["__file","arrow.vue"]]);const Cu=RB({virtualRef:{type:Object},virtualTriggering:Boolean,onMouseenter:{type:Function},onMouseleave:{type:Function},onClick:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onContextmenu:{type:Function},id:String,open:Boolean}),Uu=Symbol("elForwardRef"),Fu=A=>{if(A.tabIndex>0||0===A.tabIndex&&null!==A.getAttribute("tabIndex"))return!0;if(A.tabIndex<0||A.hasAttribute("disabled")||"true"===A.getAttribute("aria-disabled"))return!1;switch(A.nodeName){case"A":return!!A.href&&"ignore"!==A.rel;case"INPUT":return!("hidden"===A.type||"file"===A.type);case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},yu=U({name:"ElOnlyChild",setup(A,{slots:e,attrs:r}){var n;const s=t(Uu),o=(i=null!=(n=null==s?void 0:s.setForwardRef)?n:x,{mounted(A){i(A)},updated(A){i(A)},unmounted(){i(null)}});var i;return()=>{var A;const t=null==(A=e.default)?void 0:A.call(e,r);if(!t)return null;if(t.length>1)return null;const n=vu(t);return n?K(L(n,r),[[o]]):null}}});function vu(A){if(!A)return null;const e=A;for(const t of e){if(f(t))switch(t.type){case T:continue;case S:case"svg":return mu(t);case D:return vu(t.children);default:return t}return mu(t)}return null}function mu(A){const e=Fl("only-child");return M("span",{class:e.e("content")},[A])}const bu=U({name:"ElPopperTrigger",inheritAttrs:!1});var Eu=WB(U({...bu,props:Cu,setup(A,{expose:e}){const r=A,{role:o,triggerRef:i}=t(uu,void 0);var a;a=i,C(Uu,{setForwardRef:A=>{a.value=A}});const l=n((()=>B.value?r.id:void 0)),c=n((()=>{if(o&&"tooltip"===o.value)return r.open&&r.id?r.id:void 0})),B=n((()=>{if(o&&"tooltip"!==o.value)return o.value})),d=n((()=>B.value?`${r.open}`:void 0));let w;const f=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return u((()=>{g((()=>r.virtualRef),(A=>{A&&(i.value=cB(A))}),{immediate:!0}),g(i,((A,e)=>{null==w||w(),w=void 0,Zc(A)&&(f.forEach((t=>{var n;const s=r[t];s&&(A.addEventListener(t.slice(2).toLowerCase(),s),null==(n=null==e?void 0:e.removeEventListener)||n.call(e,t.slice(2).toLowerCase(),s))})),Fu(A)&&(w=g([l,c,B,d],(e=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(((t,r)=>{Nc(e[r])?A.removeAttribute(t):A.setAttribute(t,e[r])}))}),{immediate:!0}))),Zc(e)&&Fu(e)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((A=>e.removeAttribute(A)))}),{immediate:!0})})),E((()=>{if(null==w||w(),w=void 0,i.value&&Zc(i.value)){const A=i.value;f.forEach((e=>{const t=r[e];t&&A.removeEventListener(e.slice(2).toLowerCase(),t)})),i.value=void 0}})),e({triggerRef:i}),(A,e)=>A.virtualTriggering?k("v-if",!0):(y(),O(s(yu),m({key:0},A.$attrs,{"aria-controls":s(l),"aria-describedby":s(c),"aria-expanded":s(d),"aria-haspopup":s(B)}),{default:_((()=>[v(A.$slots,"default")])),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}}),[["__file","trigger.vue"]]);const Hu="focus-trap.focus-after-trapped",Iu="focus-trap.focus-after-released",xu={cancelable:!0,bubbles:!1},Ku={cancelable:!0,bubbles:!1},Lu="focusAfterTrapped",Du="focusAfterReleased",Su=Symbol("elFocusTrap"),Tu=r(),Mu=r(0),Ou=r(0);let ku=0;const _u=A=>{const e=[],t=document.createTreeWalker(A,NodeFilter.SHOW_ELEMENT,{acceptNode:A=>{const e="INPUT"===A.tagName&&"hidden"===A.type;return A.disabled||A.hidden||e?NodeFilter.FILTER_SKIP:A.tabIndex>=0||A===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)e.push(t.currentNode);return e},Ru=(A,e)=>{for(const t of A)if(!Pu(t,e))return t},Pu=(A,e)=>{if("hidden"===getComputedStyle(A).visibility)return!0;for(;A;){if(e&&A===e)return!1;if("none"===getComputedStyle(A).display)return!0;A=A.parentElement}return!1},Vu=(A,e)=>{if(A&&A.focus){const t=document.activeElement;let r=!1;!Zc(A)||Fu(A)||A.getAttribute("tabindex")||(A.setAttribute("tabindex","-1"),r=!0),A.focus({preventScroll:!0}),Ou.value=window.performance.now(),A!==t&&(A=>A instanceof HTMLInputElement&&"select"in A)(A)&&e&&A.select(),Zc(A)&&r&&A.removeAttribute("tabindex")}};function Nu(A,e){const t=[...A],r=A.indexOf(e);return-1!==r&&t.splice(r,1),t}const Gu=(()=>{let A=[];return{push:e=>{const t=A[0];t&&e!==t&&t.pause(),A=Nu(A,e),A.unshift(e)},remove:e=>{var t,r;A=Nu(A,e),null==(r=null==(t=A[0])?void 0:t.resume)||r.call(t)}}})(),Ju=()=>{Tu.value="pointer",Mu.value=window.performance.now()},Xu=()=>{Tu.value="keyboard",Mu.value=window.performance.now()},Yu=A=>new CustomEvent("focus-trap.focusout-prevented",{...Ku,detail:A}),Wu="Tab",Zu="Enter",ju="Space",zu="Escape",$u="NumpadEnter";let qu=[];const Ad=A=>{A.code===zu&&qu.forEach((e=>e(A)))};var ed=WB(U({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[Lu,Du,"focusin","focusout","focusout-prevented","release-requested"],setup(A,{emit:e}){const t=r();let n,i;const{focusReason:a}=(u((()=>{0===ku&&(document.addEventListener("mousedown",Ju),document.addEventListener("touchstart",Ju),document.addEventListener("keydown",Xu)),ku++})),E((()=>{ku--,ku<=0&&(document.removeEventListener("mousedown",Ju),document.removeEventListener("touchstart",Ju),document.removeEventListener("keydown",Xu))})),{focusReason:Tu,lastUserFocusTimestamp:Mu,lastAutomatedFocusTimestamp:Ou});var l;l=t=>{A.trapped&&!c.paused&&e("release-requested",t)},u((()=>{0===qu.length&&document.addEventListener("keydown",Ad),sB&&qu.push(l)})),E((()=>{qu=qu.filter((A=>A!==l)),0===qu.length&&sB&&document.removeEventListener("keydown",Ad)}));const c={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},B=t=>{if(!A.loop&&!A.trapped)return;if(c.paused)return;const{code:r,altKey:n,ctrlKey:s,metaKey:o,currentTarget:i,shiftKey:l}=t,{loop:B}=A,u=r===Wu&&!n&&!s&&!o,d=document.activeElement;if(u&&d){const A=i,[r,n]=(A=>{const e=_u(A);return[Ru(e,A),Ru(e.reverse(),A)]})(A);if(r&&n)if(l||d!==n){if(l&&[r,A].includes(d)){const A=Yu({focusReason:a.value});e("focusout-prevented",A),A.defaultPrevented||(t.preventDefault(),B&&Vu(n,!0))}}else{const A=Yu({focusReason:a.value});e("focusout-prevented",A),A.defaultPrevented||(t.preventDefault(),B&&Vu(r,!0))}else if(d===A){const A=Yu({focusReason:a.value});e("focusout-prevented",A),A.defaultPrevented||t.preventDefault()}}};C(Su,{focusTrapRef:t,onKeydown:B}),g((()=>A.focusTrapEl),(A=>{A&&(t.value=A)}),{immediate:!0}),g([t],(([A],[e])=>{A&&(A.addEventListener("keydown",B),A.addEventListener("focusin",p),A.addEventListener("focusout",Q)),e&&(e.removeEventListener("keydown",B),e.removeEventListener("focusin",p),e.removeEventListener("focusout",Q))}));const w=A=>{e(Lu,A)},f=A=>e(Du,A),p=r=>{const o=s(t);if(!o)return;const a=r.target,l=r.relatedTarget,B=a&&o.contains(a);if(!A.trapped){l&&o.contains(l)||(n=l)}B&&e("focusin",r),c.paused||A.trapped&&(B?i=a:Vu(i,!0))},Q=r=>{const n=s(t);if(!c.paused&&n)if(A.trapped){const t=r.relatedTarget;Nc(t)||n.contains(t)||setTimeout((()=>{if(!c.paused&&A.trapped){const A=Yu({focusReason:a.value});e("focusout-prevented",A),A.defaultPrevented||Vu(i,!0)}}),0)}else{const A=r.target;A&&n.contains(A)||e("focusout",r)}};async function h(){await d();const e=s(t);if(e){Gu.push(c);const t=e.contains(document.activeElement)?n:document.activeElement;n=t;if(!e.contains(t)){const r=new Event(Hu,xu);e.addEventListener(Hu,w),e.dispatchEvent(r),r.defaultPrevented||d((()=>{let r=A.focusStartEl;o(r)||(Vu(r),document.activeElement!==r&&(r="first")),"first"===r&&((A,e=!1)=>{const t=document.activeElement;for(const r of A)if(Vu(r,e),document.activeElement!==t)return})(_u(e),!0),document.activeElement!==t&&"container"!==r||Vu(e)}))}}}function U(){const A=s(t);if(A){A.removeEventListener(Hu,w);const e=new CustomEvent(Iu,{...xu,detail:{focusReason:a.value}});A.addEventListener(Iu,f),A.dispatchEvent(e),e.defaultPrevented||"keyboard"!=a.value&&Mu.value>Ou.value&&!A.contains(document.activeElement)||Vu(null!=n?n:document.body),A.removeEventListener(Iu,f),Gu.remove(c)}}return u((()=>{A.trapped&&h(),g((()=>A.trapped),(A=>{A?h():U()}))})),E((()=>{A.trapped&&U(),t.value&&(t.value.removeEventListener("keydown",B),t.value.removeEventListener("focusin",p),t.value.removeEventListener("focusout",Q),t.value=void 0)})),{onKeydown:B}}}),[["render",function(A,e,t,r,n,s){return v(A.$slots,"default",{handleKeydown:A.onKeydown})}],["__file","focus-trap.vue"]]),td="top",rd="bottom",nd="right",sd="left",od="auto",id=[td,rd,nd,sd],ad="start",ld="end",cd="viewport",Bd="popper",ud=id.reduce((function(A,e){return A.concat([e+"-"+ad,e+"-"+ld])}),[]),dd=[].concat(id,[od]).reduce((function(A,e){return A.concat([e,e+"-"+ad,e+"-"+ld])}),[]),gd=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function wd(A){return A?(A.nodeName||"").toLowerCase():null}function fd(A){if(null==A)return window;if("[object Window]"!==A.toString()){var e=A.ownerDocument;return e&&e.defaultView||window}return A}function pd(A){return A instanceof fd(A).Element||A instanceof Element}function Qd(A){return A instanceof fd(A).HTMLElement||A instanceof HTMLElement}function hd(A){return"undefined"!=typeof ShadowRoot&&(A instanceof fd(A).ShadowRoot||A instanceof ShadowRoot)}const Cd={name:"applyStyles",enabled:!0,phase:"write",fn:function(A){var e=A.state;Object.keys(e.elements).forEach((function(A){var t=e.styles[A]||{},r=e.attributes[A]||{},n=e.elements[A];Qd(n)&&wd(n)&&(Object.assign(n.style,t),Object.keys(r).forEach((function(A){var e=r[A];!1===e?n.removeAttribute(A):n.setAttribute(A,!0===e?"":e)})))}))},effect:function(A){var e=A.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach((function(A){var r=e.elements[A],n=e.attributes[A]||{},s=Object.keys(e.styles.hasOwnProperty(A)?e.styles[A]:t[A]).reduce((function(A,e){return A[e]="",A}),{});Qd(r)&&wd(r)&&(Object.assign(r.style,s),Object.keys(n).forEach((function(A){r.removeAttribute(A)})))}))}},requires:["computeStyles"]};function Ud(A){return A.split("-")[0]}var Fd=Math.max,yd=Math.min,vd=Math.round;function md(){var A=navigator.userAgentData;return null!=A&&A.brands&&Array.isArray(A.brands)?A.brands.map((function(A){return A.brand+"/"+A.version})).join(" "):navigator.userAgent}function bd(){return!/^((?!chrome|android).)*safari/i.test(md())}function Ed(A,e,t){void 0===e&&(e=!1),void 0===t&&(t=!1);var r=A.getBoundingClientRect(),n=1,s=1;e&&Qd(A)&&(n=A.offsetWidth>0&&vd(r.width)/A.offsetWidth||1,s=A.offsetHeight>0&&vd(r.height)/A.offsetHeight||1);var o=(pd(A)?fd(A):window).visualViewport,i=!bd()&&t,a=(r.left+(i&&o?o.offsetLeft:0))/n,l=(r.top+(i&&o?o.offsetTop:0))/s,c=r.width/n,B=r.height/s;return{width:c,height:B,top:l,right:a+c,bottom:l+B,left:a,x:a,y:l}}function Hd(A){var e=Ed(A),t=A.offsetWidth,r=A.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:A.offsetLeft,y:A.offsetTop,width:t,height:r}}function Id(A,e){var t=e.getRootNode&&e.getRootNode();if(A.contains(e))return!0;if(t&&hd(t)){var r=e;do{if(r&&A.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function xd(A){return fd(A).getComputedStyle(A)}function Kd(A){return["table","td","th"].indexOf(wd(A))>=0}function Ld(A){return((pd(A)?A.ownerDocument:A.document)||window.document).documentElement}function Dd(A){return"html"===wd(A)?A:A.assignedSlot||A.parentNode||(hd(A)?A.host:null)||Ld(A)}function Sd(A){return Qd(A)&&"fixed"!==xd(A).position?A.offsetParent:null}function Td(A){for(var e=fd(A),t=Sd(A);t&&Kd(t)&&"static"===xd(t).position;)t=Sd(t);return t&&("html"===wd(t)||"body"===wd(t)&&"static"===xd(t).position)?e:t||function(A){var e=/firefox/i.test(md());if(/Trident/i.test(md())&&Qd(A)&&"fixed"===xd(A).position)return null;var t=Dd(A);for(hd(t)&&(t=t.host);Qd(t)&&["html","body"].indexOf(wd(t))<0;){var r=xd(t);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||e&&"filter"===r.willChange||e&&r.filter&&"none"!==r.filter)return t;t=t.parentNode}return null}(A)||e}function Md(A){return["top","bottom"].indexOf(A)>=0?"x":"y"}function Od(A,e,t){return Fd(A,yd(e,t))}function kd(A){return Object.assign({},{top:0,right:0,bottom:0,left:0},A)}function _d(A,e){return e.reduce((function(e,t){return e[t]=A,e}),{})}const Rd={name:"arrow",enabled:!0,phase:"main",fn:function(A){var e,t=A.state,r=A.name,n=A.options,s=t.elements.arrow,o=t.modifiersData.popperOffsets,i=Ud(t.placement),a=Md(i),l=[sd,nd].indexOf(i)>=0?"height":"width";if(s&&o){var c=function(A,e){return kd("number"!=typeof(A="function"==typeof A?A(Object.assign({},e.rects,{placement:e.placement})):A)?A:_d(A,id))}(n.padding,t),B=Hd(s),u="y"===a?td:sd,d="y"===a?rd:nd,g=t.rects.reference[l]+t.rects.reference[a]-o[a]-t.rects.popper[l],w=o[a]-t.rects.reference[a],f=Td(s),p=f?"y"===a?f.clientHeight||0:f.clientWidth||0:0,Q=g/2-w/2,h=c[u],C=p-B[l]-c[d],U=p/2-B[l]/2+Q,F=Od(h,U,C),y=a;t.modifiersData[r]=((e={})[y]=F,e.centerOffset=F-U,e)}},effect:function(A){var e=A.state,t=A.options.element,r=void 0===t?"[data-popper-arrow]":t;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&Id(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Pd(A){return A.split("-")[1]}var Vd={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Nd(A){var e,t=A.popper,r=A.popperRect,n=A.placement,s=A.variation,o=A.offsets,i=A.position,a=A.gpuAcceleration,l=A.adaptive,c=A.roundOffsets,B=A.isFixed,u=o.x,d=void 0===u?0:u,g=o.y,w=void 0===g?0:g,f="function"==typeof c?c({x:d,y:w}):{x:d,y:w};d=f.x,w=f.y;var p=o.hasOwnProperty("x"),Q=o.hasOwnProperty("y"),h=sd,C=td,U=window;if(l){var F=Td(t),y="clientHeight",v="clientWidth";if(F===fd(t)&&"static"!==xd(F=Ld(t)).position&&"absolute"===i&&(y="scrollHeight",v="scrollWidth"),n===td||(n===sd||n===nd)&&s===ld)C=rd,w-=(B&&F===U&&U.visualViewport?U.visualViewport.height:F[y])-r.height,w*=a?1:-1;if(n===sd||(n===td||n===rd)&&s===ld)h=nd,d-=(B&&F===U&&U.visualViewport?U.visualViewport.width:F[v])-r.width,d*=a?1:-1}var m,b=Object.assign({position:i},l&&Vd),E=!0===c?function(A,e){var t=A.x,r=A.y,n=e.devicePixelRatio||1;return{x:vd(t*n)/n||0,y:vd(r*n)/n||0}}({x:d,y:w},fd(t)):{x:d,y:w};return d=E.x,w=E.y,a?Object.assign({},b,((m={})[C]=Q?"0":"",m[h]=p?"0":"",m.transform=(U.devicePixelRatio||1)<=1?"translate("+d+"px, "+w+"px)":"translate3d("+d+"px, "+w+"px, 0)",m)):Object.assign({},b,((e={})[C]=Q?w+"px":"",e[h]=p?d+"px":"",e.transform="",e))}var Gd={passive:!0};var Jd={left:"right",right:"left",bottom:"top",top:"bottom"};function Xd(A){return A.replace(/left|right|bottom|top/g,(function(A){return Jd[A]}))}var Yd={start:"end",end:"start"};function Wd(A){return A.replace(/start|end/g,(function(A){return Yd[A]}))}function Zd(A){var e=fd(A);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function jd(A){return Ed(Ld(A)).left+Zd(A).scrollLeft}function zd(A){var e=xd(A),t=e.overflow,r=e.overflowX,n=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+n+r)}function $d(A){return["html","body","#document"].indexOf(wd(A))>=0?A.ownerDocument.body:Qd(A)&&zd(A)?A:$d(Dd(A))}function qd(A,e){var t;void 0===e&&(e=[]);var r=$d(A),n=r===(null==(t=A.ownerDocument)?void 0:t.body),s=fd(r),o=n?[s].concat(s.visualViewport||[],zd(r)?r:[]):r,i=e.concat(o);return n?i:i.concat(qd(Dd(o)))}function Ag(A){return Object.assign({},A,{left:A.x,top:A.y,right:A.x+A.width,bottom:A.y+A.height})}function eg(A,e,t){return e===cd?Ag(function(A,e){var t=fd(A),r=Ld(A),n=t.visualViewport,s=r.clientWidth,o=r.clientHeight,i=0,a=0;if(n){s=n.width,o=n.height;var l=bd();(l||!l&&"fixed"===e)&&(i=n.offsetLeft,a=n.offsetTop)}return{width:s,height:o,x:i+jd(A),y:a}}(A,t)):pd(e)?function(A,e){var t=Ed(A,!1,"fixed"===e);return t.top=t.top+A.clientTop,t.left=t.left+A.clientLeft,t.bottom=t.top+A.clientHeight,t.right=t.left+A.clientWidth,t.width=A.clientWidth,t.height=A.clientHeight,t.x=t.left,t.y=t.top,t}(e,t):Ag(function(A){var e,t=Ld(A),r=Zd(A),n=null==(e=A.ownerDocument)?void 0:e.body,s=Fd(t.scrollWidth,t.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),o=Fd(t.scrollHeight,t.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),i=-r.scrollLeft+jd(A),a=-r.scrollTop;return"rtl"===xd(n||t).direction&&(i+=Fd(t.clientWidth,n?n.clientWidth:0)-s),{width:s,height:o,x:i,y:a}}(Ld(A)))}function tg(A,e,t,r){var n="clippingParents"===e?function(A){var e=qd(Dd(A)),t=["absolute","fixed"].indexOf(xd(A).position)>=0&&Qd(A)?Td(A):A;return pd(t)?e.filter((function(A){return pd(A)&&Id(A,t)&&"body"!==wd(A)})):[]}(A):[].concat(e),s=[].concat(n,[t]),o=s[0],i=s.reduce((function(e,t){var n=eg(A,t,r);return e.top=Fd(n.top,e.top),e.right=yd(n.right,e.right),e.bottom=yd(n.bottom,e.bottom),e.left=Fd(n.left,e.left),e}),eg(A,o,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function rg(A){var e,t=A.reference,r=A.element,n=A.placement,s=n?Ud(n):null,o=n?Pd(n):null,i=t.x+t.width/2-r.width/2,a=t.y+t.height/2-r.height/2;switch(s){case td:e={x:i,y:t.y-r.height};break;case rd:e={x:i,y:t.y+t.height};break;case nd:e={x:t.x+t.width,y:a};break;case sd:e={x:t.x-r.width,y:a};break;default:e={x:t.x,y:t.y}}var l=s?Md(s):null;if(null!=l){var c="y"===l?"height":"width";switch(o){case ad:e[l]=e[l]-(t[c]/2-r[c]/2);break;case ld:e[l]=e[l]+(t[c]/2-r[c]/2)}}return e}function ng(A,e){void 0===e&&(e={});var t=e,r=t.placement,n=void 0===r?A.placement:r,s=t.strategy,o=void 0===s?A.strategy:s,i=t.boundary,a=void 0===i?"clippingParents":i,l=t.rootBoundary,c=void 0===l?cd:l,B=t.elementContext,u=void 0===B?Bd:B,d=t.altBoundary,g=void 0!==d&&d,w=t.padding,f=void 0===w?0:w,p=kd("number"!=typeof f?f:_d(f,id)),Q=u===Bd?"reference":Bd,h=A.rects.popper,C=A.elements[g?Q:u],U=tg(pd(C)?C:C.contextElement||Ld(A.elements.popper),a,c,o),F=Ed(A.elements.reference),y=rg({reference:F,element:h,placement:n}),v=Ag(Object.assign({},h,y)),m=u===Bd?v:F,b={top:U.top-m.top+p.top,bottom:m.bottom-U.bottom+p.bottom,left:U.left-m.left+p.left,right:m.right-U.right+p.right},E=A.modifiersData.offset;if(u===Bd&&E){var H=E[n];Object.keys(b).forEach((function(A){var e=[nd,rd].indexOf(A)>=0?1:-1,t=[td,rd].indexOf(A)>=0?"y":"x";b[A]+=H[t]*e}))}return b}const sg={name:"flip",enabled:!0,phase:"main",fn:function(A){var e=A.state,t=A.options,r=A.name;if(!e.modifiersData[r]._skip){for(var n=t.mainAxis,s=void 0===n||n,o=t.altAxis,i=void 0===o||o,a=t.fallbackPlacements,l=t.padding,c=t.boundary,B=t.rootBoundary,u=t.altBoundary,d=t.flipVariations,g=void 0===d||d,w=t.allowedAutoPlacements,f=e.options.placement,p=Ud(f),Q=a||(p===f||!g?[Xd(f)]:function(A){if(Ud(A)===od)return[];var e=Xd(A);return[Wd(A),e,Wd(e)]}(f)),h=[f].concat(Q).reduce((function(A,t){return A.concat(Ud(t)===od?function(A,e){void 0===e&&(e={});var t=e,r=t.placement,n=t.boundary,s=t.rootBoundary,o=t.padding,i=t.flipVariations,a=t.allowedAutoPlacements,l=void 0===a?dd:a,c=Pd(r),B=c?i?ud:ud.filter((function(A){return Pd(A)===c})):id,u=B.filter((function(A){return l.indexOf(A)>=0}));0===u.length&&(u=B);var d=u.reduce((function(e,t){return e[t]=ng(A,{placement:t,boundary:n,rootBoundary:s,padding:o})[Ud(t)],e}),{});return Object.keys(d).sort((function(A,e){return d[A]-d[e]}))}(e,{placement:t,boundary:c,rootBoundary:B,padding:l,flipVariations:g,allowedAutoPlacements:w}):t)}),[]),C=e.rects.reference,U=e.rects.popper,F=new Map,y=!0,v=h[0],m=0;m<h.length;m++){var b=h[m],E=Ud(b),H=Pd(b)===ad,I=[td,rd].indexOf(E)>=0,x=I?"width":"height",K=ng(e,{placement:b,boundary:c,rootBoundary:B,altBoundary:u,padding:l}),L=I?H?nd:sd:H?rd:td;C[x]>U[x]&&(L=Xd(L));var D=Xd(L),S=[];if(s&&S.push(K[E]<=0),i&&S.push(K[L]<=0,K[D]<=0),S.every((function(A){return A}))){v=b,y=!1;break}F.set(b,S)}if(y)for(var T=function(A){var e=h.find((function(e){var t=F.get(e);if(t)return t.slice(0,A).every((function(A){return A}))}));if(e)return v=e,"break"},M=g?3:1;M>0;M--){if("break"===T(M))break}e.placement!==v&&(e.modifiersData[r]._skip=!0,e.placement=v,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function og(A,e,t){return void 0===t&&(t={x:0,y:0}),{top:A.top-e.height-t.y,right:A.right-e.width+t.x,bottom:A.bottom-e.height+t.y,left:A.left-e.width-t.x}}function ig(A){return[td,nd,rd,sd].some((function(e){return A[e]>=0}))}const ag={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(A){var e=A.state,t=A.name,r=e.rects.reference,n=e.rects.popper,s=e.modifiersData.preventOverflow,o=ng(e,{elementContext:"reference"}),i=ng(e,{altBoundary:!0}),a=og(o,r),l=og(i,n,s),c=ig(a),B=ig(l);e.modifiersData[t]={referenceClippingOffsets:a,popperEscapeOffsets:l,isReferenceHidden:c,hasPopperEscaped:B},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":B})}};const lg={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(A){var e=A.state,t=A.options,r=A.name,n=t.offset,s=void 0===n?[0,0]:n,o=dd.reduce((function(A,t){return A[t]=function(A,e,t){var r=Ud(A),n=[sd,td].indexOf(r)>=0?-1:1,s="function"==typeof t?t(Object.assign({},e,{placement:A})):t,o=s[0],i=s[1];return o=o||0,i=(i||0)*n,[sd,nd].indexOf(r)>=0?{x:i,y:o}:{x:o,y:i}}(t,e.rects,s),A}),{}),i=o[e.placement],a=i.x,l=i.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=a,e.modifiersData.popperOffsets.y+=l),e.modifiersData[r]=o}};const cg={name:"popperOffsets",enabled:!0,phase:"read",fn:function(A){var e=A.state,t=A.name;e.modifiersData[t]=rg({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}};const Bg={name:"preventOverflow",enabled:!0,phase:"main",fn:function(A){var e=A.state,t=A.options,r=A.name,n=t.mainAxis,s=void 0===n||n,o=t.altAxis,i=void 0!==o&&o,a=t.boundary,l=t.rootBoundary,c=t.altBoundary,B=t.padding,u=t.tether,d=void 0===u||u,g=t.tetherOffset,w=void 0===g?0:g,f=ng(e,{boundary:a,rootBoundary:l,padding:B,altBoundary:c}),p=Ud(e.placement),Q=Pd(e.placement),h=!Q,C=Md(p),U="x"===C?"y":"x",F=e.modifiersData.popperOffsets,y=e.rects.reference,v=e.rects.popper,m="function"==typeof w?w(Object.assign({},e.rects,{placement:e.placement})):w,b="number"==typeof m?{mainAxis:m,altAxis:m}:Object.assign({mainAxis:0,altAxis:0},m),E=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,H={x:0,y:0};if(F){if(s){var I,x="y"===C?td:sd,K="y"===C?rd:nd,L="y"===C?"height":"width",D=F[C],S=D+f[x],T=D-f[K],M=d?-v[L]/2:0,O=Q===ad?y[L]:v[L],k=Q===ad?-v[L]:-y[L],_=e.elements.arrow,R=d&&_?Hd(_):{width:0,height:0},P=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},V=P[x],N=P[K],G=Od(0,y[L],R[L]),J=h?y[L]/2-M-G-V-b.mainAxis:O-G-V-b.mainAxis,X=h?-y[L]/2+M+G+N+b.mainAxis:k+G+N+b.mainAxis,Y=e.elements.arrow&&Td(e.elements.arrow),W=Y?"y"===C?Y.clientTop||0:Y.clientLeft||0:0,Z=null!=(I=null==E?void 0:E[C])?I:0,j=D+X-Z,z=Od(d?yd(S,D+J-Z-W):S,D,d?Fd(T,j):T);F[C]=z,H[C]=z-D}if(i){var $,q="x"===C?td:sd,AA="x"===C?rd:nd,eA=F[U],tA="y"===U?"height":"width",rA=eA+f[q],nA=eA-f[AA],sA=-1!==[td,sd].indexOf(p),oA=null!=($=null==E?void 0:E[U])?$:0,iA=sA?rA:eA-y[tA]-v[tA]-oA+b.altAxis,aA=sA?eA+y[tA]+v[tA]-oA-b.altAxis:nA,lA=d&&sA?(BA=Od(iA,eA,cA=aA))>cA?cA:BA:Od(d?iA:rA,eA,d?aA:nA);F[U]=lA,H[U]=lA-eA}var cA,BA;e.modifiersData[r]=H}},requiresIfExists:["offset"]};function ug(A,e,t){void 0===t&&(t=!1);var r,n,s=Qd(e),o=Qd(e)&&function(A){var e=A.getBoundingClientRect(),t=vd(e.width)/A.offsetWidth||1,r=vd(e.height)/A.offsetHeight||1;return 1!==t||1!==r}(e),i=Ld(e),a=Ed(A,o,t),l={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(s||!s&&!t)&&(("body"!==wd(e)||zd(i))&&(l=(r=e)!==fd(r)&&Qd(r)?{scrollLeft:(n=r).scrollLeft,scrollTop:n.scrollTop}:Zd(r)),Qd(e)?((c=Ed(e,!0)).x+=e.clientLeft,c.y+=e.clientTop):i&&(c.x=jd(i))),{x:a.left+l.scrollLeft-c.x,y:a.top+l.scrollTop-c.y,width:a.width,height:a.height}}function dg(A){var e=new Map,t=new Set,r=[];function n(A){t.add(A.name),[].concat(A.requires||[],A.requiresIfExists||[]).forEach((function(A){if(!t.has(A)){var r=e.get(A);r&&n(r)}})),r.push(A)}return A.forEach((function(A){e.set(A.name,A)})),A.forEach((function(A){t.has(A.name)||n(A)})),r}var gg={placement:"bottom",modifiers:[],strategy:"absolute"};function wg(){for(var A=arguments.length,e=new Array(A),t=0;t<A;t++)e[t]=arguments[t];return!e.some((function(A){return!(A&&"function"==typeof A.getBoundingClientRect)}))}function fg(A){void 0===A&&(A={});var e=A,t=e.defaultModifiers,r=void 0===t?[]:t,n=e.defaultOptions,s=void 0===n?gg:n;return function(A,e,t){void 0===t&&(t=s);var n,o,i={placement:"bottom",orderedModifiers:[],options:Object.assign({},gg,s),modifiersData:{},elements:{reference:A,popper:e},attributes:{},styles:{}},a=[],l=!1,c={state:i,setOptions:function(t){var n="function"==typeof t?t(i.options):t;B(),i.options=Object.assign({},s,i.options,n),i.scrollParents={reference:pd(A)?qd(A):A.contextElement?qd(A.contextElement):[],popper:qd(e)};var o,l,u=function(A){var e=dg(A);return gd.reduce((function(A,t){return A.concat(e.filter((function(A){return A.phase===t})))}),[])}((o=[].concat(r,i.options.modifiers),l=o.reduce((function(A,e){var t=A[e.name];return A[e.name]=t?Object.assign({},t,e,{options:Object.assign({},t.options,e.options),data:Object.assign({},t.data,e.data)}):e,A}),{}),Object.keys(l).map((function(A){return l[A]}))));return i.orderedModifiers=u.filter((function(A){return A.enabled})),i.orderedModifiers.forEach((function(A){var e=A.name,t=A.options,r=void 0===t?{}:t,n=A.effect;if("function"==typeof n){var s=n({state:i,name:e,instance:c,options:r}),o=function(){};a.push(s||o)}})),c.update()},forceUpdate:function(){if(!l){var A=i.elements,e=A.reference,t=A.popper;if(wg(e,t)){i.rects={reference:ug(e,Td(t),"fixed"===i.options.strategy),popper:Hd(t)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(A){return i.modifiersData[A.name]=Object.assign({},A.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var n=i.orderedModifiers[r],s=n.fn,o=n.options,a=void 0===o?{}:o,B=n.name;"function"==typeof s&&(i=s({state:i,options:a,name:B,instance:c})||i)}else i.reset=!1,r=-1}}},update:(n=function(){return new Promise((function(A){c.forceUpdate(),A(i)}))},function(){return o||(o=new Promise((function(A){Promise.resolve().then((function(){o=void 0,A(n())}))}))),o}),destroy:function(){B(),l=!0}};if(!wg(A,e))return c;function B(){a.forEach((function(A){return A()})),a=[]}return c.setOptions(t).then((function(A){!l&&t.onFirstUpdate&&t.onFirstUpdate(A)})),c}}var pg=fg({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(A){var e=A.state,t=A.instance,r=A.options,n=r.scroll,s=void 0===n||n,o=r.resize,i=void 0===o||o,a=fd(e.elements.popper),l=[].concat(e.scrollParents.reference,e.scrollParents.popper);return s&&l.forEach((function(A){A.addEventListener("scroll",t.update,Gd)})),i&&a.addEventListener("resize",t.update,Gd),function(){s&&l.forEach((function(A){A.removeEventListener("scroll",t.update,Gd)})),i&&a.removeEventListener("resize",t.update,Gd)}},data:{}},cg,{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(A){var e=A.state,t=A.options,r=t.gpuAcceleration,n=void 0===r||r,s=t.adaptive,o=void 0===s||s,i=t.roundOffsets,a=void 0===i||i,l={placement:Ud(e.placement),variation:Pd(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:n,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Nd(Object.assign({},l,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:o,roundOffsets:a})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Nd(Object.assign({},l,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},Cd,lg,sg,Bg,Rd,ag]});const Qg=RB({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:Array,default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:dd,default:"bottom"},popperOptions:{type:Object,default:()=>({})},strategy:{type:String,values:["fixed","absolute"],default:"absolute"}}),hg=RB({...Qg,id:String,style:{type:[String,Array,Object]},className:{type:[String,Array,Object]},effect:{type:String,default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:[String,Array,Object]},popperStyle:{type:[String,Array,Object]},referenceEl:{type:Object},triggerTargetEl:{type:Object},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...iu(["ariaLabel"])}),Cg={mouseenter:A=>A instanceof MouseEvent,mouseleave:A=>A instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},Ug=(A,e=[])=>{const{placement:t,strategy:r,popperOptions:n}=A,s={placement:t,strategy:r,...n,modifiers:[...Fg(A),...e]};return function(A,e){e&&(A.modifiers=[...A.modifiers,...null!=e?e:[]])}(s,null==n?void 0:n.modifiers),s};function Fg(A){const{offset:e,gpuAcceleration:t,fallbackPlacements:r}=A;return[{name:"offset",options:{offset:[0,null!=e?e:12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:r}},{name:"computeStyles",options:{gpuAcceleration:t}}]}const yg=(A,e,t={})=>{const o={name:"updateState",enabled:!0,phase:"write",fn:({state:A})=>{const e=function(A){const e=Object.keys(A.elements),t=Vc(e.map((e=>[e,A.styles[e]||{}]))),r=Vc(e.map((e=>[e,A.attributes[e]])));return{styles:t,attributes:r}}(A);Object.assign(c.value,e)},requires:["computeStyles"]},a=n((()=>{const{onFirstUpdate:A,placement:e,strategy:r,modifiers:n}=s(t);return{onFirstUpdate:A,placement:e||"bottom",strategy:r||"absolute",modifiers:[...n||[],o,{name:"applyStyles",enabled:!1}]}})),l=i(),c=r({styles:{popper:{position:s(a).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),B=()=>{l.value&&(l.value.destroy(),l.value=void 0)};return g(a,(A=>{const e=s(l);e&&e.setOptions(A)}),{deep:!0}),g([A,e],(([A,e])=>{B(),A&&e&&(l.value=pg(A,e,s(a)))})),E((()=>{B()})),{state:n((()=>{var A;return{...(null==(A=s(l))?void 0:A.state)||{}}})),styles:n((()=>s(c).styles)),attributes:n((()=>s(c).attributes)),update:()=>{var A;return null==(A=s(l))?void 0:A.update()},forceUpdate:()=>{var A;return null==(A=s(l))?void 0:A.forceUpdate()},instanceRef:n((()=>s(l)))}};const vg=A=>{const{popperInstanceRef:e,contentRef:o,triggerRef:i,role:a}=t(uu,void 0),l=r(),c=r(),B=n((()=>({name:"eventListeners",enabled:!!A.visible}))),d=n((()=>{var A;const e=s(l),t=null!=(A=s(c))?A:0;return{name:"arrow",enabled:(r=e,!(void 0===r)),options:{element:e,padding:t}};var r})),w=n((()=>({onFirstUpdate:()=>{C()},...Ug(A,[s(d),s(B)])}))),f=n((()=>(A=>{if(sB)return cB(A)})(A.referenceEl)||s(i))),{attributes:p,state:Q,styles:h,update:C,forceUpdate:U,instanceRef:F}=yg(f,o,w);return g(F,(A=>e.value=A),{flush:"sync"}),u((()=>{g((()=>{var A;return null==(A=s(f))?void 0:A.getBoundingClientRect()}),(()=>{C()}))})),{attributes:p,arrowRef:l,contentRef:o,instanceRef:F,state:Q,styles:h,role:a,forceUpdate:U,update:C}},mg=U({name:"ElPopperContent"});var bg=WB(U({...mg,props:hg,emits:Cg,setup(A,{expose:e,emit:o}){const i=A,{focusStartRef:a,trapped:l,onFocusAfterReleased:c,onFocusAfterTrapped:B,onFocusInTrap:d,onFocusoutPrevented:w,onReleaseRequested:f}=((A,e)=>{const t=r(!1),n=r();return{focusStartRef:n,trapped:t,onFocusAfterReleased:A=>{var t;"pointer"!==(null==(t=A.detail)?void 0:t.focusReason)&&(n.value="first",e("blur"))},onFocusAfterTrapped:()=>{e("focus")},onFocusInTrap:e=>{A.visible&&!t.value&&(e.target&&(n.value=e.target),t.value=!0)},onFocusoutPrevented:e=>{A.trapping||("pointer"===e.detail.focusReason&&e.preventDefault(),t.value=!1)},onReleaseRequested:()=>{t.value=!1,e("close")}}})(i,o),{attributes:p,arrowRef:Q,contentRef:h,styles:U,instanceRef:b,role:H,update:I}=vg(i),{ariaModal:K,arrowStyle:L,contentAttrs:D,contentClass:S,contentStyle:T,updateZIndex:O}=((A,{attributes:e,styles:t,role:o})=>{const{nextZIndex:i}=LB(),a=Fl("popper"),l=n((()=>s(e).popper)),c=r(Wc(A.zIndex)?A.zIndex:i()),B=n((()=>[a.b(),a.is("pure",A.pure),a.is(A.effect),A.popperClass])),u=n((()=>[{zIndex:s(c)},s(t).popper,A.popperStyle||{}]));return{ariaModal:n((()=>"dialog"===o.value?"false":void 0)),arrowStyle:n((()=>s(t).arrow||{})),contentAttrs:l,contentClass:B,contentStyle:u,contentZIndex:c,updateZIndex:()=>{c.value=Wc(A.zIndex)?A.zIndex:i()}}})(i,{styles:U,attributes:p,role:H}),k=t(au,void 0),R=r();let P;C(du,{arrowStyle:L,arrowRef:Q,arrowOffset:R}),k&&C(au,{...k,addInputId:x,removeInputId:x});const V=(A=!0)=>{I(),A&&O()},N=()=>{V(!1),i.visible&&i.focusOnShow?l.value=!0:!1===i.visible&&(l.value=!1)};return u((()=>{g((()=>i.triggerTargetEl),((A,e)=>{null==P||P(),P=void 0;const t=s(A||h.value),r=s(e||h.value);Zc(t)&&(P=g([H,()=>i.ariaLabel,K,()=>i.id],(A=>{["role","aria-label","aria-modal","id"].forEach(((e,r)=>{Nc(A[r])?t.removeAttribute(e):t.setAttribute(e,A[r])}))}),{immediate:!0})),r!==t&&Zc(r)&&["role","aria-label","aria-modal","id"].forEach((A=>{r.removeAttribute(A)}))}),{immediate:!0}),g((()=>i.visible),N,{immediate:!0})})),E((()=>{null==P||P(),P=void 0})),e({popperContentRef:h,popperInstanceRef:b,updatePopper:V,contentStyle:T}),(A,e)=>(y(),F("div",m({ref_key:"contentRef",ref:h},s(D),{style:s(T),class:s(S),tabindex:"-1",onMouseenter:e=>A.$emit("mouseenter",e),onMouseleave:e=>A.$emit("mouseleave",e)}),[M(s(ed),{trapped:s(l),"trap-on-focus-in":!0,"focus-trap-el":s(h),"focus-start-el":s(a),onFocusAfterTrapped:s(B),onFocusAfterReleased:s(c),onFocusin:s(d),onFocusoutPrevented:s(w),onReleaseRequested:s(f)},{default:_((()=>[v(A.$slots,"default")])),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}}),[["__file","content.vue"]]);const Eg=jB(fu),Hg=Symbol("elTooltip");function Ig(){let A;const e=()=>window.clearTimeout(A);return lB((()=>e())),{registerTimeout:(t,r)=>{e(),A=window.setTimeout(t,r)},cancelTimeout:e}}const xg=RB({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),Kg=RB({to:{type:[String,Object],required:!0},disabled:Boolean}),Lg=RB({...xg,...hg,appendTo:{type:Kg.to.type},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:Boolean,default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...iu(["ariaLabel"])}),Dg=RB({...Cu,disabled:Boolean,trigger:{type:[String,Array],default:"hover"},triggerKeys:{type:Array,default:()=>[Zu,$u,ju]}}),Sg=_B({type:Boolean,default:null}),Tg=_B({type:Function}),{useModelToggleProps:Mg,useModelToggleEmits:Og,useModelToggle:kg}=(A=>{const t=`update:${A}`,r=`onUpdate:${A}`,s=[t];return{useModelToggle:({indicator:s,toggleReason:o,shouldHideWhenRouteChanges:i,shouldProceed:a,onShow:l,onHide:c})=>{const B=e(),{emit:d}=B,w=B.props,f=n((()=>h(w[r]))),p=n((()=>null===w[A])),Q=A=>{!0!==s.value&&(s.value=!0,o&&(o.value=A),h(l)&&l(A))},C=A=>{!1!==s.value&&(s.value=!1,o&&(o.value=A),h(c)&&c(A))},U=A=>{if(!0===w.disabled||h(a)&&!a())return;const e=f.value&&sB;e&&d(t,!0),!p.value&&e||Q(A)},F=A=>{if(!0===w.disabled||!sB)return;const e=f.value&&sB;e&&d(t,!1),!p.value&&e||C(A)},y=A=>{Yc(A)&&(w.disabled&&A?f.value&&d(t,!1):s.value!==A&&(A?Q():C()))};return g((()=>w[A]),y),i&&void 0!==B.appContext.config.globalProperties.$route&&g((()=>({...B.proxy.$route})),(()=>{i.value&&s.value&&F()})),u((()=>{y(w[A])})),{hide:F,show:U,toggle:()=>{s.value?F():U()},hasUpdateHandler:f}},useModelToggleProps:{[A]:Sg,[r]:Tg},useModelToggleEmits:s}})("visible"),_g=RB({...gu,...Mg,...Lg,...Dg,...pu,showArrow:{type:Boolean,default:!0}}),Rg=[...Og,"before-show","before-hide","show","hide","open","close"],Pg=(A,e,t)=>r=>{((A,e)=>R(A)?A.includes(e):A===e)(s(A),e)&&t(r)},Vg=(A,e,{checkForDefaultPrevented:t=!0}={})=>r=>{const n=null==A?void 0:A(r);if(!1===t||!n)return null==e?void 0:e(r)},Ng=U({name:"ElTooltipTrigger"});var Gg=WB(U({...Ng,props:Dg,setup(A,{expose:e}){const n=A,o=Fl("tooltip"),{controlled:i,id:a,open:l,onOpen:c,onClose:B,onToggle:u}=t(Hg,void 0),d=r(null),g=()=>{if(s(i)||n.disabled)return!0},w=P(n,"trigger"),f=Vg(g,Pg(w,"hover",c)),p=Vg(g,Pg(w,"hover",B)),Q=Vg(g,Pg(w,"click",(A=>{0===A.button&&u(A)}))),h=Vg(g,Pg(w,"focus",c)),C=Vg(g,Pg(w,"focus",B)),U=Vg(g,Pg(w,"contextmenu",(A=>{A.preventDefault(),u(A)}))),F=Vg(g,(A=>{const{code:e}=A;n.triggerKeys.includes(e)&&(A.preventDefault(),u(A))}));return e({triggerRef:d}),(A,e)=>(y(),O(s(Eu),{id:s(a),"virtual-ref":A.virtualRef,open:s(l),"virtual-triggering":A.virtualTriggering,class:I(s(o).e("trigger")),onBlur:s(C),onClick:s(Q),onContextmenu:s(U),onFocus:s(h),onMouseenter:s(f),onMouseleave:s(p),onKeydown:s(F)},{default:_((()=>[v(A.$slots,"default")])),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}}),[["__file","trigger.vue"]]);const Jg=jB(WB(U({__name:"teleport",props:Kg,setup:A=>(A,e)=>A.disabled?v(A.$slots,"default",{key:0}):(y(),O(V,{key:1,to:A.to},[v(A.$slots,"default")],8,["to"]))}),[["__file","teleport.vue"]])),Xg=()=>{const A=Ul(),e=Bu(),t=n((()=>`${A.value}-popper-container-${e.prefix}`)),r=n((()=>`#${t.value}`));return{id:t,selector:r}},Yg=()=>{const{id:A,selector:e}=Xg();return N((()=>{sB&&(document.body.querySelector(e.value)||(A=>{const e=document.createElement("div");e.id=A,document.body.appendChild(e)})(A.value))})),{id:A,selector:e}},Wg=U({name:"ElTooltipContent",inheritAttrs:!1}),Zg=U({...Wg,props:Lg,setup(A,{expose:e}){const o=A,{selector:i}=Xg(),a=Fl("tooltip"),l=r(),c=nB((()=>{var A;return null==(A=l.value)?void 0:A.popperContentRef}));let B;const{controlled:u,id:d,open:w,trigger:f,onClose:p,onOpen:Q,onShow:h,onHide:C,onBeforeShow:U,onBeforeHide:F}=t(Hg,void 0),b=n((()=>o.transition||`${a.namespace.value}-fade-in-linear`)),H=n((()=>o.persistent));E((()=>{null==B||B()}));const I=n((()=>!!s(H)||s(w))),x=n((()=>!o.disabled&&s(w))),L=n((()=>o.appendTo||i.value)),D=n((()=>{var A;return null!=(A=o.style)?A:{}})),S=r(!0),T=()=>{C(),Z()&&Vu(document.body),S.value=!0},R=()=>{if(s(u))return!0},P=Vg(R,(()=>{o.enterable&&"hover"===s(f)&&Q()})),V=Vg(R,(()=>{"hover"===s(f)&&p()})),N=()=>{var A,e;null==(e=null==(A=l.value)?void 0:A.updatePopper)||e.call(A),null==U||U()},X=()=>{null==F||F()},Y=()=>{h(),B=function(A,e,t={}){const{window:r=BB,ignore:n=[],capture:s=!0,detectIframe:o=!1}=t;if(!r)return;iB&&!dB&&(dB=!0,Array.from(r.document.body.children).forEach((A=>A.addEventListener("click",oB))));let i=!0;const a=A=>n.some((e=>{if("string"==typeof e)return Array.from(r.document.querySelectorAll(e)).some((e=>e===A.target||A.composedPath().includes(e)));{const t=cB(e);return t&&(A.target===t||A.composedPath().includes(t))}})),l=[uB(r,"click",(t=>{const r=cB(A);r&&r!==t.target&&!t.composedPath().includes(r)&&(0===t.detail&&(i=!a(t)),i?e(t):i=!0)}),{passive:!0,capture:s}),uB(r,"pointerdown",(e=>{const t=cB(A);t&&(i=!e.composedPath().includes(t)&&!a(e))}),{passive:!0}),o&&uB(r,"blur",(t=>{var n;const s=cB(A);"IFRAME"!==(null==(n=r.document.activeElement)?void 0:n.tagName)||(null==s?void 0:s.contains(r.document.activeElement))||e(t)}))].filter(Boolean);return()=>l.forEach((A=>A()))}(c,(()=>{if(s(u))return;"hover"!==s(f)&&p()}))},W=()=>{o.virtualTriggering||p()},Z=A=>{var e;const t=null==(e=l.value)?void 0:e.popperContentRef,r=(null==A?void 0:A.relatedTarget)||document.activeElement;return null==t?void 0:t.contains(r)};return g((()=>s(w)),(A=>{A?S.value=!1:null==B||B()}),{flush:"post"}),g((()=>o.content),(()=>{var A,e;null==(e=null==(A=l.value)?void 0:A.updatePopper)||e.call(A)})),e({contentRef:l,isFocusInsideContent:Z}),(A,e)=>(y(),O(s(Jg),{disabled:!A.teleported,to:s(L)},{default:_((()=>[M(G,{name:s(b),onAfterLeave:T,onBeforeEnter:N,onAfterEnter:Y,onBeforeLeave:X},{default:_((()=>[s(I)?K((y(),O(s(bg),m({key:0,id:s(d),ref_key:"contentRef",ref:l},A.$attrs,{"aria-label":A.ariaLabel,"aria-hidden":S.value,"boundaries-padding":A.boundariesPadding,"fallback-placements":A.fallbackPlacements,"gpu-acceleration":A.gpuAcceleration,offset:A.offset,placement:A.placement,"popper-options":A.popperOptions,strategy:A.strategy,effect:A.effect,enterable:A.enterable,pure:A.pure,"popper-class":A.popperClass,"popper-style":[A.popperStyle,s(D)],"reference-el":A.referenceEl,"trigger-target-el":A.triggerTargetEl,visible:s(x),"z-index":A.zIndex,onMouseenter:s(P),onMouseleave:s(V),onBlur:W,onClose:s(p)}),{default:_((()=>[v(A.$slots,"default")])),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[J,s(x)]]):k("v-if",!0)])),_:3},8,["name"])])),_:3},8,["disabled","to"]))}});var jg=WB(Zg,[["__file","content.vue"]]);const zg=U({name:"ElTooltip"});const $g=jB(WB(U({...zg,props:_g,emits:Rg,setup(A,{expose:e,emit:t}){const o=A;Yg();const i=Fl("tooltip"),a=(A=>{const e=Bu(),t=Ul();return nB((()=>s(A)||`${t.value}-id-${e.prefix}-${e.current++}`))})(),c=r(),B=r(),u=()=>{var A;const e=s(c);e&&(null==(A=e.popperInstanceRef)||A.update())},d=r(!1),w=r(),{show:f,hide:p,hasUpdateHandler:Q}=kg({indicator:d,toggleReason:w}),{onOpen:h,onClose:U}=(({showAfter:A,hideAfter:e,autoClose:t,open:r,close:n})=>{const{registerTimeout:o}=Ig(),{registerTimeout:i,cancelTimeout:a}=Ig();return{onOpen:e=>{o((()=>{r(e);const A=s(t);Wc(A)&&A>0&&i((()=>{n(e)}),A)}),s(A))},onClose:A=>{a(),o((()=>{n(A)}),s(e))}}})({showAfter:P(o,"showAfter"),hideAfter:P(o,"hideAfter"),autoClose:P(o,"autoClose"),open:f,close:p}),m=n((()=>Yc(o.visible)&&!Q.value)),b=n((()=>[i.b(),o.popperClass]));C(Hg,{controlled:m,id:a,open:l(d),trigger:P(o,"trigger"),onOpen:A=>{h(A)},onClose:A=>{U(A)},onToggle:A=>{s(d)?U(A):h(A)},onShow:()=>{t("show",w.value)},onHide:()=>{t("hide",w.value)},onBeforeShow:()=>{t("before-show",w.value)},onBeforeHide:()=>{t("before-hide",w.value)},updatePopper:u}),g((()=>o.disabled),(A=>{A&&d.value&&(d.value=!1)}));return X((()=>d.value&&p())),e({popperRef:c,contentRef:B,isFocusInsideContent:A=>{var e;return null==(e=B.value)?void 0:e.isFocusInsideContent(A)},updatePopper:u,onOpen:h,onClose:U,hide:p}),(A,e)=>(y(),O(s(Eg),{ref_key:"popperRef",ref:c,role:A.role},{default:_((()=>[M(Gg,{disabled:A.disabled,trigger:A.trigger,"trigger-keys":A.triggerKeys,"virtual-ref":A.virtualRef,"virtual-triggering":A.virtualTriggering},{default:_((()=>[A.$slots.default?v(A.$slots,"default",{key:0}):k("v-if",!0)])),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),M(jg,{ref_key:"contentRef",ref:B,"aria-label":A.ariaLabel,"boundaries-padding":A.boundariesPadding,content:A.content,disabled:A.disabled,effect:A.effect,enterable:A.enterable,"fallback-placements":A.fallbackPlacements,"hide-after":A.hideAfter,"gpu-acceleration":A.gpuAcceleration,offset:A.offset,persistent:A.persistent,"popper-class":s(b),"popper-style":A.popperStyle,placement:A.placement,"popper-options":A.popperOptions,pure:A.pure,"raw-content":A.rawContent,"reference-el":A.referenceEl,"trigger-target-el":A.triggerTargetEl,"show-after":A.showAfter,strategy:A.strategy,teleported:A.teleported,transition:A.transition,"virtual-triggering":A.virtualTriggering,"z-index":A.zIndex,"append-to":A.appendTo},{default:_((()=>[v(A.$slots,"content",{},(()=>[A.rawContent?(y(),F("span",{key:0,innerHTML:A.content},null,8,["innerHTML"])):(y(),F("span",{key:1},Y(A.content),1))])),A.showArrow?(y(),O(s(hu),{key:0,"arrow-offset":A.arrowOffset},null,8,["arrow-offset"])):k("v-if",!0)])),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])])),_:3},8,["role"]))}}),[["__file","tooltip.vue"]])),qg=RB({value:{type:[String,Number],default:""},max:{type:Number,default:99},isDot:Boolean,hidden:Boolean,type:{type:String,values:["primary","success","warning","info","danger"],default:"danger"},showZero:{type:Boolean,default:!0},color:String,badgeStyle:{type:[String,Object,Array]},offset:{type:Array,default:[0,0]},badgeClass:{type:String}}),Aw=U({name:"ElBadge"});const ew=jB(WB(U({...Aw,props:qg,setup(A,{expose:e}){const t=A,r=Fl("badge"),o=n((()=>t.isDot?"":Wc(t.value)&&Wc(t.max)&&t.max<t.value?`${t.max}+`:`${t.value}`)),i=n((()=>{var A,e,r,n,s;return[{backgroundColor:t.color,marginRight:ZB(-(null!=(e=null==(A=t.offset)?void 0:A[0])?e:0)),marginTop:ZB(null!=(n=null==(r=t.offset)?void 0:r[1])?n:0)},null!=(s=t.badgeStyle)?s:{}]}));return e({content:o}),(A,e)=>(y(),F("div",{class:I(s(r).b())},[v(A.$slots,"default"),M(G,{name:`${s(r).namespace.value}-zoom-in-center`,persisted:""},{default:_((()=>[K(b("sup",{class:I([s(r).e("content"),s(r).em("content",A.type),s(r).is("fixed",!!A.$slots.default),s(r).is("dot",A.isDot),s(r).is("hide-zero",!A.showZero&&0===t.value),A.badgeClass]),style:H(s(i))},[v(A.$slots,"content",{value:s(o)},(()=>[W(Y(s(o)),1)]))],6),[[J,!A.hidden&&(s(o)||A.isDot||A.$slots.content)]])])),_:3},8,["name"])],2))}}),[["__file","badge.vue"]])),tw={},rw=["success","info","warning","error"],nw={customClass:"",dangerouslyUseHTMLString:!1,duration:3e3,icon:void 0,id:"",message:"",onClose:void 0,showClose:!1,type:"info",plain:!1,offset:16,zIndex:0,grouping:!1,repeatNum:1,appendTo:sB?document.body:void 0},sw=RB({customClass:{type:String,default:nw.customClass},dangerouslyUseHTMLString:{type:Boolean,default:nw.dangerouslyUseHTMLString},duration:{type:Number,default:nw.duration},icon:{type:ru,default:nw.icon},id:{type:String,default:nw.id},message:{type:[String,Object,Function],default:nw.message},onClose:{type:Function,default:nw.onClose},showClose:{type:Boolean,default:nw.showClose},type:{type:String,values:rw,default:nw.type},plain:{type:Boolean,default:nw.plain},offset:{type:Number,default:nw.offset},zIndex:{type:Number,default:nw.zIndex},grouping:{type:Boolean,default:nw.grouping},repeatNum:{type:Number,default:nw.repeatNum}}),ow=Z([]),iw=A=>{const{prev:e}=(A=>{const e=ow.findIndex((e=>e.id===A)),t=ow[e];let r;return e>0&&(r=ow[e-1]),{current:t,prev:r}})(A);return e?e.vm.exposed.bottom.value:0},aw=U({name:"ElMessage"}),lw=U({...aw,props:sw,emits:{destroy:()=>!0},setup(A,{expose:e,emit:t}){const o=A,{Close:i}=nu,a=r(!1),{ns:c,zIndex:B}=function(A,e){const t=JB(),r=Fl(A,n((()=>{var A;return(null==(A=t.value)?void 0:A.namespace)||Ql}))),o=OB(n((()=>{var A;return null==(A=t.value)?void 0:A.locale}))),i=LB(n((()=>{var A;return(null==(A=t.value)?void 0:A.zIndex)||2e3}))),a=n((()=>{var A;return s(e)||(null==(A=t.value)?void 0:A.size)||""}));return XB(n((()=>s(t)||{}))),{ns:r,locale:o,zIndex:i,size:a}}("message"),{currentZIndex:w,nextZIndex:f}=B,p=r(),Q=r(!1),h=r(0);let C;const U=n((()=>o.type?"error"===o.type?"danger":o.type:"info")),m=n((()=>{const A=o.type;return{[c.bm("icon",A)]:A&&su[A]}})),E=n((()=>o.icon||su[o.type]||"")),x=n((()=>iw(o.id))),L=n((()=>((A,e)=>ow.findIndex((e=>e.id===A))>0?16:e)(o.id,o.offset)+x.value)),S=n((()=>h.value+L.value)),T=n((()=>({top:`${L.value}px`,zIndex:w.value})));function R(){0!==o.duration&&({stop:C}=function(A,e,t={}){const{immediate:n=!0}=t,s=r(!1);let o=null;function i(){o&&(clearTimeout(o),o=null)}function a(){s.value=!1,i()}function c(...t){i(),s.value=!0,o=setTimeout((()=>{s.value=!1,o=null,A(...t)}),aB(e))}return n&&(s.value=!0,sB&&c()),lB(a),{isPending:l(s),start:c,stop:a}}((()=>{V()}),o.duration))}function P(){null==C||C()}function V(){Q.value=!1,d((()=>{var A;a.value||(null==(A=o.onClose)||A.call(o),t("destroy"))}))}return u((()=>{R(),f(),Q.value=!0})),g((()=>o.repeatNum),(()=>{P(),R()})),uB(document,"keydown",(function({code:A}){A===zu&&V()})),FB(p,(()=>{h.value=p.value.getBoundingClientRect().height})),e({visible:Q,bottom:S,close:V}),(A,e)=>(y(),O(G,{name:s(c).b("fade"),onBeforeEnter:A=>a.value=!0,onBeforeLeave:A.onClose,onAfterLeave:e=>A.$emit("destroy"),persisted:""},{default:_((()=>[K(b("div",{id:A.id,ref_key:"messageRef",ref:p,class:I([s(c).b(),{[s(c).m(A.type)]:A.type},s(c).is("closable",A.showClose),s(c).is("plain",A.plain),A.customClass]),style:H(s(T)),role:"alert",onMouseenter:P,onMouseleave:R},[A.repeatNum>1?(y(),O(s(ew),{key:0,value:A.repeatNum,type:s(U),class:I(s(c).e("badge"))},null,8,["value","type","class"])):k("v-if",!0),s(E)?(y(),O(s(qB),{key:1,class:I([s(c).e("icon"),s(m)])},{default:_((()=>[(y(),O(j(s(E))))])),_:1},8,["class"])):k("v-if",!0),v(A.$slots,"default",{},(()=>[A.dangerouslyUseHTMLString?(y(),F(D,{key:1},[k(" Caution here, message could've been compromised, never use user's input as message "),b("p",{class:I(s(c).e("content")),innerHTML:A.message},null,10,["innerHTML"])],2112)):(y(),F("p",{key:0,class:I(s(c).e("content"))},Y(A.message),3))])),A.showClose?(y(),O(s(qB),{key:2,class:I(s(c).e("closeBtn")),onClick:z(V,["stop"])},{default:_((()=>[M(s(i))])),_:1},8,["class","onClick"])):k("v-if",!0)],46,["id"]),[[J,Q.value]])])),_:3},8,["name","onBeforeEnter","onBeforeLeave","onAfterLeave"]))}});var cw=WB(lw,[["__file","message.vue"]]);let Bw=1;const uw=A=>{const e=!A||o(A)||$(A)||h(A)?{message:A}:A,t={...nw,...e};if(t.appendTo){if(o(t.appendTo)){let A=document.querySelector(t.appendTo);Zc(A)||(A=document.body),t.appendTo=A}}else t.appendTo=document.body;return Yc(tw.grouping)&&!t.grouping&&(t.grouping=tw.grouping),Wc(tw.duration)&&3e3===t.duration&&(t.duration=tw.duration),Wc(tw.offset)&&16===t.offset&&(t.offset=tw.offset),Yc(tw.showClose)&&!t.showClose&&(t.showClose=tw.showClose),t},dw=({appendTo:A,...e},t)=>{const r="message_"+Bw++,n=e.onClose,s=document.createElement("div"),o={...e,id:r,onClose:()=>{null==n||n(),(A=>{const e=ow.indexOf(A);if(-1===e)return;ow.splice(e,1);const{handler:t}=A;t.close()})(c)},onDestroy:()=>{q(null,s)}},i=M(cw,o,h(o.message)||$(o.message)?{default:h(o.message)?o.message:()=>o.message}:null);i.appContext=t||gw._context,q(i,s),A.appendChild(s.firstElementChild);const a=i.component,l={close:()=>{a.exposed.close()}},c={id:r,vnode:i,vm:a,handler:l,props:i.component.props};return c},gw=(A={},e)=>{if(!sB)return{close:()=>{}};const t=uw(A);if(t.grouping&&ow.length){const A=ow.find((({vnode:A})=>{var e;return(null==(e=A.props)?void 0:e.message)===t.message}));if(A)return A.props.repeatNum+=1,A.props.type=t.type,A.handler}if(Wc(tw.max)&&ow.length>=tw.max)return{close:()=>{}};const r=dw(t,e);return ow.push(r),r.handler};rw.forEach((A=>{gw[A]=(e={},t)=>{const r=uw(e);return gw({...r,type:A},t)}})),gw.closeAll=function(A){const e=[...ow];for(const t of e)A&&A!==t.props.type||t.handler.close()},gw._context=null;const ww=(pw="$message",(fw=gw).install=A=>{fw._context=A._context,A.config.globalProperties[pw]=fw},fw);var fw,pw;const Qw=[{name:"Gate.io",baseUrl:"gate.io",tradeUrlPatterns:["/zh/trade/","/en/trade/","/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)_USDT/i,/\/[a-z]{2}\/trade\/([A-Z0-9]+)_USDT/i,/\/([A-Z0-9]+)_USDT$/i]},{name:"Binance",baseUrl:"binance.com",tradeUrlPatterns:["/*/trade/","/trade/"],symbolRegexes:[/\/([A-Z0-9]+)USDT$/i,/\/trade\/([A-Z0-9]+)_USDT/i,/\/trading\/([A-Z0-9]+)USDT/i]},{name:"OKX",baseUrl:"okx.com",tradeUrlPatterns:["/*/trade-spot/","/*/trade/","/trade-spot/","/trade/"],symbolRegexes:[/\/([A-Z0-9]+)-USDT$/i,/\/trade\/([A-Z0-9]+)-USDT/i,/\/spot\/([A-Z0-9]+)-USDT/i]},{name:"HTX",baseUrl:"htx.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([a-zA-Z0-9]+)_([a-zA-Z0-9]+)/i]},{name:"Bybit",baseUrl:"bybit.com",tradeUrlPatterns:["/trade/usdt/"],symbolRegexes:[/\/trade\/usdt\/([A-Z0-9]+)/i]},{name:"Bitmart",baseUrl:"bitmart.com",tradeUrlPatterns:["/trade/en"],symbolRegexes:[/\/trade\/en\?symbol=([A-Z0-9_]+)/i]},{name:"Coinbase",baseUrl:"coinbase.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9-]+)/i]},{name:"Bitstamp",baseUrl:"bitstamp.net",tradeUrlPatterns:["/markets/"],symbolRegexes:[/\/markets\/([a-z0-9]+)\/([a-z0-9]+)/i]},{name:"Kucoin",baseUrl:"kucoin.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/(\w+)-(\w+)/i]},{name:"Poloniex",baseUrl:"poloniex.com",tradeUrlPatterns:["/spot/"],symbolRegexes:[/\/spot\/([A-Z0-9_]+)/i]},{name:"Bithumb",baseUrl:"bithumb.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Upbit",baseUrl:"upbit.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\?code=CRIX.UPBIT.([A-Z0-9]+)-([A-Z0-9]+)/i]},{name:"Bitflyer",baseUrl:"bitflyer.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Gemini",baseUrl:"gemini.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)[-_/]([A-Z0-9]+)/i]},{name:"LBank",baseUrl:"lbank.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9_]+)/i]},{name:"Phemex",baseUrl:"phemex.com",tradeUrlPatterns:["/spot/trade"],symbolRegexes:[/\/spot\/trade\?symbol=([A-Z0-9_]+)/i]},{name:"MEXC",baseUrl:"mexc.com",tradeUrlPatterns:["/exchange/","/futures/"],symbolRegexes:[/\/exchange\/([A-Z0-9_]+)/i,/\/futures\/([A-Z0-9_]+)/i]},{name:"Bitget",baseUrl:"bitget.com",tradeUrlPatterns:["/spot/"],symbolRegexes:[/\/spot\/([A-Z0-9_]+)/i]},{name:"Bitfinex",baseUrl:"bitfinex.com",tradeUrlPatterns:["/t/"],symbolRegexes:[/\/t\/([A-Z0-9:]+)/i]},{name:"Kraken",baseUrl:"kraken.com",tradeUrlPatterns:["/trade/"],symbolRegexes:[/\/trade\/([A-Z0-9]+)-([A-Z0-9]+)/i]},{name:"Huobi",baseUrl:"huobi.com",tradeUrlPatterns:["/en-us/exchange/"],symbolRegexes:[/\/exchange\/([a-z0-9]+)\/\?type=spot/i]}],hw=A=>{try{if(A.includes("mexc.com")&&A.includes("/futures/")){const e=A.match(/\/futures\/([A-Z0-9_]+)/i);if(e){const A=e[1];if(A.includes("_")){return A.split("_")[0].toUpperCase()+"USDT"}}}for(const e of Qw)if(A.includes(e.baseUrl))for(const t of e.symbolRegexes){const e=A.match(t);if(e){return e.slice(1).map((A=>A.toUpperCase())).join("")}}return null}catch(e){return null}};function Cw(A){var e,t,r,n,s,o,i,a,l,c,B,u,d,g,w;try{if(!A)throw new Error("技术分析数据为空");if(function(A){return"object"==typeof A&&null!==A&&"status"in A&&"string"==typeof A.status}(A)&&"data"in A){if("success"!==A.status)throw new Error(`API响应错误: ${A.status}`);if(!A.data)throw new Error("API响应中data为空");A=A.data}if(function(A){return"object"==typeof A&&null!==A&&"trend_up_probability"in A&&"trend_sideways_probability"in A&&"trend_down_probability"in A}(A))try{return{current_price:"number"==typeof A.current_price?A.current_price:0,snapshot_price:"number"==typeof A.snapshot_price?A.snapshot_price:"number"==typeof A.current_price?A.current_price:0,trend_analysis:{probabilities:{up:"number"==typeof A.trend_up_probability?A.trend_up_probability:0,sideways:"number"==typeof A.trend_sideways_probability?A.trend_sideways_probability:0,down:"number"==typeof A.trend_down_probability?A.trend_down_probability:0},summary:"string"==typeof A.trend_summary?A.trend_summary:"无数据"},indicators_analysis:A.indicators_analysis||{},trading_advice:{action:"string"==typeof A.trading_action?A.trading_action:"无建议",reason:"string"==typeof A.trading_reason?A.trading_reason:"无数据",entry_price:"number"==typeof A.entry_price?A.entry_price:0,stop_loss:"number"==typeof A.stop_loss?A.stop_loss:0,take_profit:"number"==typeof A.take_profit?A.take_profit:0},risk_assessment:{level:"string"==typeof A.risk_level?A.risk_level:"中",score:"number"==typeof A.risk_score?A.risk_score:50,details:Array.isArray(A.risk_details)?A.risk_details:[]},last_update_time:"string"==typeof A.last_update_time?A.last_update_time:(new Date).toISOString()}}catch(f){throw new Error(`格式化强制刷新数据失败: ${f instanceof Error?f.message:String(f)}`)}if(function(A){return"object"==typeof A&&null!==A&&"trend_analysis"in A&&"indicators_analysis"in A&&"trading_advice"in A&&"risk_assessment"in A}(A))try{return{current_price:"number"==typeof A.current_price?A.current_price:0,snapshot_price:"number"==typeof A.snapshot_price?A.snapshot_price:"number"==typeof A.current_price?A.current_price:0,trend_analysis:{probabilities:{up:"number"==typeof(null==(t=null==(e=A.trend_analysis)?void 0:e.probabilities)?void 0:t.up)?A.trend_analysis.probabilities.up:0,sideways:"number"==typeof(null==(n=null==(r=A.trend_analysis)?void 0:r.probabilities)?void 0:n.sideways)?A.trend_analysis.probabilities.sideways:0,down:"number"==typeof(null==(o=null==(s=A.trend_analysis)?void 0:s.probabilities)?void 0:o.down)?A.trend_analysis.probabilities.down:0},summary:"string"==typeof(null==(i=A.trend_analysis)?void 0:i.summary)?A.trend_analysis.summary:"无数据"},indicators_analysis:A.indicators_analysis||{},trading_advice:{action:"string"==typeof(null==(a=A.trading_advice)?void 0:a.action)?A.trading_advice.action:"无建议",reason:"string"==typeof(null==(l=A.trading_advice)?void 0:l.reason)?A.trading_advice.reason:"无数据",entry_price:"number"==typeof(null==(c=A.trading_advice)?void 0:c.entry_price)?A.trading_advice.entry_price:0,stop_loss:"number"==typeof(null==(B=A.trading_advice)?void 0:B.stop_loss)?A.trading_advice.stop_loss:0,take_profit:"number"==typeof(null==(u=A.trading_advice)?void 0:u.take_profit)?A.trading_advice.take_profit:0},risk_assessment:{level:"string"==typeof(null==(d=A.risk_assessment)?void 0:d.level)?A.risk_assessment.level:"中",score:"number"==typeof(null==(g=A.risk_assessment)?void 0:g.score)?A.risk_assessment.score:50,details:Array.isArray(null==(w=A.risk_assessment)?void 0:w.details)?A.risk_assessment.details:[]},last_update_time:"string"==typeof A.last_update_time?A.last_update_time:(new Date).toISOString()}}catch(f){throw new Error(`格式化技术分析数据失败: ${f instanceof Error?f.message:String(f)}`)}throw new Error("无法识别的数据格式")}catch(f){return{current_price:0,snapshot_price:0,trend_analysis:{probabilities:{up:.33,sideways:.34,down:.33},summary:"数据加载失败，请刷新重试"},indicators_analysis:{RSI:{value:0,analysis:"数据加载失败",support_trend:"中性"},MACD:{value:{line:0,signal:0,histogram:0},analysis:"数据加载失败",support_trend:"中性"},BollingerBands:{value:{upper:0,middle:0,lower:0},analysis:"数据加载失败",support_trend:"中性"},BIAS:{value:0,analysis:"数据加载失败",support_trend:"中性"},PSY:{value:0,analysis:"数据加载失败",support_trend:"中性"},DMI:{value:{plus_di:0,minus_di:0,adx:0},analysis:"数据加载失败",support_trend:"中性"},VWAP:{value:0,analysis:"数据加载失败",support_trend:"中性"},FundingRate:{value:0,analysis:"数据加载失败",support_trend:"中性"},ExchangeNetflow:{value:0,analysis:"数据加载失败",support_trend:"中性"},NUPL:{value:0,analysis:"数据加载失败",support_trend:"中性"},MayerMultiple:{value:0,analysis:"数据加载失败",support_trend:"中性"}},trading_advice:{action:"无建议",reason:"数据加载失败",entry_price:0,stop_loss:0,take_profit:0},risk_assessment:{level:"中",score:50,details:["数据加载失败，无法评估风险"]},last_update_time:(new Date).toISOString()}}}Qw.map((A=>({name:A.name,baseUrl:A.baseUrl})));const Uw={class:"flex flex-col items-center justify-center h-full text-center px-4"},Fw={class:"text-xl font-semibold text-white mb-2"},yw=["disabled"],vw={key:0,class:"flex items-center justify-center"},mw={key:1,class:"flex items-center justify-center"},bw=U({__name:"TokenNotFoundView",props:{symbol:{},isRefreshing:{type:Boolean}},emits:["refresh"],setup(A,{emit:e}){const t=A,s=e,o=n((()=>t.symbol.toUpperCase().endsWith("USDT")?t.symbol:`${t.symbol}/USDT`)),i=r(!1);r(!0),g((()=>t.isRefreshing),(A=>{!1===A&&(i.value=!1)}));const a=()=>{i.value=!0,s("refresh"),setTimeout((()=>{i.value=!1}),6e4)},l=n((()=>!0===t.isRefreshing));return(A,e)=>(y(),F("div",Uw,[e[2]||(e[2]=b("div",{class:"mb-6"},[b("i",{class:"ri-database-2-line text-5xl text-yellow-500"})],-1)),b("h2",Fw,Y(o.value)+" 数据未找到",1),e[3]||(e[3]=b("p",{class:"text-gray-300 mb-6"},"该代币尚未在我们的数据库中，点击下方按钮获取最新数据",-1)),b("button",{class:"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium mb-4 w-full max-w-xs transition-colors duration-200 flex items-center justify-center",onClick:a,disabled:i.value||l.value},[i.value||l.value?(y(),F("span",vw,e[0]||(e[0]=[b("span",{class:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"},null,-1),W(" 正在获取最新市场数据... ")]))):(y(),F("span",mw,e[1]||(e[1]=[b("i",{class:"ri-refresh-line mr-2"},null,-1),W(" 获取最新市场数据 ")])))],8,yw)]))}}),Ew={class:"relative h-[600px] flex flex-col bg-[#0F172A]"},Hw={class:"absolute top-0 left-0 right-0 z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},Iw={class:"max-w-[375px] mx-auto"},xw={class:"flex justify-between items-center px-4 py-3"},Kw={class:"text-lg font-semibold"},Lw={class:"flex items-center space-x-2"},Dw={class:"text-xs text-gray-400"},Sw=["disabled"],Tw={class:"absolute inset-0 top-12 bottom-16 overflow-y-auto"},Mw={key:0,class:"max-w-[375px] mx-auto px-4 pb-16"},Ow={key:1,class:"flex items-center justify-center h-full"},kw={key:2,class:"flex items-center justify-center h-full"},_w={class:"text-center px-4"},Rw={class:"text-gray-300 mb-2"},Pw={key:3,class:"max-w-[375px] mx-auto px-4 pb-16"},Vw={class:"mt-6 p-5 rounded-lg bg-gradient-to-b from-gray-800/60 to-gray-900/60 border border-gray-700/50 shadow-lg"},Nw={class:"text-center text-3xl font-bold mb-2"},Gw={key:0,class:"mt-6 grid grid-cols-3 gap-3"},Jw={class:"p-3 rounded-lg bg-gradient-to-br from-green-600/20 to-green-800/20 border border-green-500/30 text-center"},Xw={class:"text-green-400 text-xl font-bold mb-1"},Yw={class:"p-3 rounded-lg bg-gradient-to-br from-gray-700/20 to-gray-800/20 border border-gray-600/30 text-center"},Ww={class:"text-gray-300 text-xl font-bold mb-1"},Zw={class:"p-3 rounded-lg bg-[rgba(239,68,68,0.12)] border border-red-500/30 text-center"},jw={class:"text-red-400 text-xl font-bold mb-1"},zw={key:1,class:"mt-6"},$w={class:"p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"},qw={class:"text-gray-300 leading-relaxed"},Af={key:2,class:"mt-6"},ef={class:"flex flex-col gap-3"},tf={class:"grid grid-cols-2 gap-3"},rf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},nf={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},sf={class:"flex items-center justify-between"},of={class:"font-medium"},af={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},lf={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},cf={class:"flex items-center justify-between"},Bf={class:"font-medium"},uf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},df={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},gf={class:"flex items-center justify-between"},wf={class:"font-medium"},ff={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},pf={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},Qf={class:"flex items-center justify-between"},hf={class:"font-medium"},Cf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},Uf={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},Ff={class:"flex items-center justify-between"},yf={class:"font-medium"},vf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},mf={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},bf={class:"flex items-center justify-between"},Ef={class:"font-medium"},Hf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},If={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},xf={class:"flex items-center justify-between"},Kf={class:"font-medium"},Lf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"},Df={class:"text-sm text-gray-400 mb-1 flex items-center gap-1"},Sf={class:"flex items-center justify-between"},Tf={class:"font-medium"},Mf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50 mt-3"},Of={class:"flex items-center justify-between mb-2"},kf={class:"text-sm text-gray-400 flex items-center gap-1"},_f={class:"grid grid-cols-3 gap-2"},Rf={class:"text-center p-1 rounded bg-blue-900/20 border border-blue-800/30"},Pf={class:"text-sm"},Vf={class:"text-center p-1 rounded bg-blue-900/20 border border-blue-800/30"},Nf={class:"text-sm"},Gf={class:"text-center p-1 rounded bg-blue-900/20 border border-blue-800/30"},Jf={class:"text-sm"},Xf={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50 mt-3"},Yf={class:"flex items-center justify-between mb-2"},Wf={class:"text-sm text-gray-400 flex items-center gap-1"},Zf={class:"grid grid-cols-3 gap-2"},jf={class:"text-center p-1 rounded bg-red-900/20 border border-red-800/30"},zf={class:"text-sm"},$f={class:"text-center p-1 rounded bg-gray-700/30 border border-gray-600/30"},qf={class:"text-sm"},Ap={class:"text-center p-1 rounded bg-green-900/20 border border-green-800/30"},ep={class:"text-sm"},tp={class:"p-3 rounded-lg bg-gray-800/30 border border-gray-700/50 mt-3"},rp={class:"flex items-center justify-between mb-2"},np={class:"text-sm text-gray-400 flex items-center gap-1"},sp={class:"grid grid-cols-3 gap-2"},op={class:"text-center p-1 rounded bg-green-900/20 border border-green-800/30"},ip={class:"text-sm"},ap={class:"text-center p-1 rounded bg-red-900/20 border border-red-800/30"},lp={class:"text-sm"},cp={class:"text-center p-1 rounded bg-blue-900/20 border border-blue-800/30"},Bp={class:"text-sm"},up={key:3,class:"mt-6"},dp={class:"p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 space-y-3"},gp={class:"flex items-center justify-between"},wp={class:"flex items-center justify-between"},fp={class:"text-sm"},pp={class:"flex items-center justify-between"},Qp={class:"text-sm text-red-400"},hp={class:"flex items-center justify-between"},Cp={class:"text-sm text-green-400"},Up={class:"pt-2 border-t border-gray-700/50"},Fp={class:"text-sm text-gray-300"},yp={key:4,class:"mt-6"},vp={class:"p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"},mp={class:"flex items-center justify-between mb-3"},bp={class:"mb-3"},Ep={class:"w-full bg-gray-700/50 rounded-full h-2"},Hp={key:0},Ip={class:"text-sm text-gray-300 list-disc pl-5 space-y-1"},xp={key:0,class:"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center"},Kp={class:"bg-gray-900 rounded-xl p-6 w-[320px] shadow-xl border border-gray-800"},Lp={class:"relative h-3 bg-gray-800 rounded-full overflow-hidden mb-2"},Dp={class:"text-center text-sm text-gray-400 mb-4"},Sp={class:"text-sm text-gray-300 text-center min-h-[48px]"},Tp={class:"absolute bottom-0 left-0 right-0 bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},Mp={class:"max-w-[375px] mx-auto"},Op={class:"grid grid-cols-2 h-16"},kp=U({__name:"HomeView",setup(e){const t=r(null),n=r(!1),o=r(!1),i=r(null),a=r(""),l=r(0),c=r(!1),B=r(!1),w=r(0),f=r("正在刷新数据...");let p=null;const Q="相对强弱指数（RSI），用于衡量价格动量和超买超卖状态。",h="乖离率，衡量价格偏离均线的程度。",C="心理线指标，反映市场参与者的心理变化。",U="成交量加权平均价，反映市场真实交易价值。",v="资金费率，反映合约市场多空力量对比。",m="交易所净流入，反映资金流向。",E="未实现净盈亏比率，反映市场整体盈亏状况。",x="梅耶倍数，当前价格与200日均线的比值。",K="移动平均线收敛散度，用于判断趋势强弱和转折点。",L="布林带，用于衡量价格波动性和支撑阻力位。",S="动向指标，用于判断趋势方向和强度。",T=A=>{if(null==A)return"--";if("string"==typeof A&&(A=parseFloat(A),isNaN(A)))return A||"--";const e=Number(A);return isNaN(e)?"--":e<1e-4?e<1e-8?e.toExponential(8):e.toFixed(8):e<1?e.toFixed(6):e.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})},O=A=>{if(!A)return"--";try{const e=new Date(A);if(isNaN(e.getTime()))return"无效时间";const t=new Date,r=Math.floor((t.getTime()-e.getTime())/1e3/60);return r<60?`${r}分钟前`:r<1440?`${Math.floor(r/60)}小时前`:`${Math.floor(r/1440)}天前`}catch(e){return console.error("时间格式化错误:",e),"时间错误"}},R=async(e=!1)=>{var r,s,B,u,d;try{n.value=!0,i.value=null,o.value=!0,e||(t.value=null);let f=null,p="";if("undefined"!=typeof chrome&&void 0!==chrome.runtime&&"function"==typeof chrome.runtime.getURL)try{const A=(await chrome.tabs.query({active:!0,currentWindow:!0}))[0];if(!A.url)return console.error("无法获取当前页面URL"),i.value="无法获取当前页面URL",void(n.value=!1);p=A.url,f=hw(p)}catch(g){return console.error("获取标签页信息失败:",g),i.value="无法访问当前标签页，请确保已授予必要权限",void(n.value=!1)}else p=window.location.href,f=hw(p);f||(f="BTC",i.value=null),a.value=f;try{try{const r=await A(f,e);if("object"==typeof r&&null!==r){if("status"in r&&"not_found"===r.status)return c.value=!0,n.value=!1,void(o.value=!1);const A=Cw(r);console.log("原始API响应:",r),console.log("格式化后的数据:",A),console.log("格式化前的价格数据:",{current_price:r.current_price,snapshot_price:r.snapshot_price}),console.log("格式化后的价格数据:",{current_price:A.current_price,snapshot_price:A.snapshot_price}),t.value=A,l.value=0,i.value=null,c.value=!1}else console.error("API返回格式错误:",r),i.value="服务器返回数据格式错误"}catch(w){console.error("API请求错误:",w);let A="请求失败";if(w.message&&(A=w.message),"ERR_NETWORK"===w.code||(null==(r=w.message)?void 0:r.includes("Network Error")))A="网络连接错误，请检查网络连接后重试";else if("ECONNABORTED"===w.code||(null==(s=w.message)?void 0:s.includes("timeout")))A="请求超时，服务器响应时间过长";else if((null==(B=w.response)?void 0:B.status)>=500)return console.log("服务器500错误，可能是代币未找到，显示TokenNotFoundView"),c.value=!0,i.value=null,n.value=!1,void(o.value=!1);if((null==(u=w.response)?void 0:u.data)&&"object"==typeof w.response.data){if("not_found"===w.response.data.status)return console.log("错误响应中发现交易对未找到状态"),c.value=!0,i.value=null,n.value=!1,void(o.value=!1)}if(404===(null==(d=w.response)?void 0:d.status))return console.log("检测到404错误，认为是交易对未找到"),c.value=!0,void(i.value=null);c.value=!1,i.value=A,console.error("设置错误信息:",i.value)}}catch(w){console.error("API请求错误:",w),i.value=w.message||"加载分析数据失败"}}catch(g){console.error("整体加载过程错误:",g),i.value||c.value||(i.value=g instanceof Error?g.message:"加载数据失败")}finally{n.value=!1,o.value=!1}};u((async()=>{await d(),setTimeout((async()=>{try{await R()}catch(A){i.value=A instanceof Error?A.message:"加载数据失败",n.value=!1}}),500)})),g(a,(async A=>{A&&await R()}));const P=A=>{if(null==A)return"--";if("string"==typeof A){const e=A.replace(/[^\d.]/g,"");if(A=parseFloat(e),isNaN(A))return e?`${e}%`:"--"}const e=Number(A);return isNaN(e)?"--":e>1?`${Math.round(e)}%`:`${Math.round(100*e)}%`},V=A=>A?"bullish"===A||"看涨"===A||"支持当前趋势"===A?"text-green-400":"bearish"===A||"看跌"===A||"不支持当前趋势"===A?"text-red-400":"neutral"===A||"中性"===A?"text-yellow-400":"text-gray-400":"text-gray-400",N=A=>"bullish"===A||"看涨"===A||"支持当前趋势"===A?"ri-arrow-up-line":"bearish"===A||"看跌"===A||"反对当前趋势"===A?"ri-arrow-down-line":"ri-subtract-line",G=()=>{null!==p&&(clearInterval(p),p=null),w.value=1,B.value=!0,f.value="正在获取市场数据并进行技术指标计算...";const A=Date.now();p=setInterval((()=>{if(!B.value)return void(null!==p&&(clearInterval(p),p=null));const e=Date.now()-A,t=Math.min(95,(A=>{const e=A/1e3;if(e<=10)return 1+39*Math.sqrt(e/10);if(e<=20)return 40+(e-10)/10*30;{const A=Math.min(1,(e-20)/10);return 70+25*(1-Math.pow(1-A,3))}})(e));w&&void 0!==w.value&&(w.value=t),f.value=t<30?"正在获取市场数据并进行技术指标计算...":t<60?"正在进行趋势分析和概率评估...":t<85?"正在生成交易建议和风险评估...":"最终数据整合中，即将完成...",t>=95&&null!==p&&(clearInterval(p),p=null)}),100)},J=async()=>{var e,r,n,s,l,u,g,Q;try{if(B.value)return;if(G(),!a.value)return void(i.value="无法获取当前交易对信息");try{f.value="正在从币安获取最新市场数据...";try{i.value=null,c.value=!1,o.value=!0;const e=await A(a.value,!0);await new Promise((A=>setTimeout(A,1e3)));const r=await A(a.value,!1),n=Cw(r||e);t.value=n,c.value=!1,o.value=!1,await d(),f.value="数据刷新完成！";const s=w.value,l=Date.now(),u=500,g=setInterval((()=>{const A=Date.now()-l,e=Math.min(1,A/u),t=1-Math.pow(1-e,2),r=s+(100-s)*t;w.value=r,e>=1&&(clearInterval(g),w.value=100,setTimeout((()=>{B.value=!1}),500))}),16)}catch(h){if(f.value="强制刷新失败，正在尝试获取缓存数据...",404===(null==(e=h.response)?void 0:e.status)||(null==(r=h.response)?void 0:r.status)>=500)return c.value=!0,void setTimeout((()=>{B.value=!1}),1e3);("not_found"===(null==(s=null==(n=h.response)?void 0:n.data)?void 0:s.status)||"object"==typeof(null==(l=h.response)?void 0:l.data)&&null!==(null==(u=h.response)?void 0:u.data)&&"status"in h.response.data&&"not_found"===h.response.data.status)&&(c.value=!0)}}catch(C){console.error("数据刷新失败:",C),(null==(g=C.message)?void 0:g.includes("Network Error"))||(null==(Q=C.message)?void 0:Q.includes("timeout"))?i.value="网络连接错误，请检查您的网络连接并重试":i.value=C.message||"刷新数据失败",f.value="加载失败，请稍后重试";const A=w.value,e=Date.now(),t=500,r=setInterval((()=>{const n=Date.now()-e,s=Math.min(1,n/t),o=1-Math.pow(1-s,2),i=A+(100-A)*o;w.value=i,s>=1&&(clearInterval(r),w.value=100,setTimeout((()=>{B.value=!1}),500))}),16)}}catch(U){console.error("强制刷新数据失败:",U),i.value=U instanceof Error?U.message:"刷新数据失败"}finally{null!==p&&(clearInterval(p),p=null)}},X=async()=>{var e;i.value=null,c.value=!1,o.value=!0;try{const e=await A(a.value,!1);if(console.log("普通刷新数据返回:",e),"object"==typeof e&&null!==e&&"status"in e){const A=e;if("not_found"===A.status&&!0===A.needs_refresh)return c.value=!0,void(o.value=!1)}const r=Cw(e);t.value=r,console.log("普通刷新后，格式化数据是否包含市场趋势分析:",!!r.trend_analysis),console.log("普通刷新后，格式化数据是否包含交易建议:",!!r.trading_advice),console.log("普通刷新后，格式化数据是否包含风险评估:",!!r.risk_assessment),o.value=!1,await d()}catch(r){o.value=!1,404===(null==(e=r.response)?void 0:e.status)?(c.value=!0,i.value=null):i.value=r.message||"刷新数据失败"}},Z=()=>{var A,e,r,n,s,o,i,l,c,B,u,d,g,w,f,p,Q,h,C,U,F,y,v,m,b;try{const E=a.value||"CRYPTO",H=T((null==(A=t.value)?void 0:A.current_price)||0);let I=null==(n=null==(r=null==(e=t.value)?void 0:e.trend_analysis)?void 0:r.probabilities)?void 0:n.up,x=null==(i=null==(o=null==(s=t.value)?void 0:s.trend_analysis)?void 0:o.probabilities)?void 0:i.down;I="number"==typeof I&&I>=0&&I<=1?I:.33,x="number"==typeof x&&x>=0&&x<=1?x:.33;const K=(null==(c=null==(l=t.value)?void 0:l.trend_analysis)?void 0:c.summary)||"无趋势分析",L=(null==(u=null==(B=t.value)?void 0:B.trading_advice)?void 0:u.action)||"无交易建议",D=(null==(g=null==(d=t.value)?void 0:d.trading_advice)?void 0:g.reason)||"",S=T(null==(f=null==(w=t.value)?void 0:w.trading_advice)?void 0:f.entry_price),M=T(null==(Q=null==(p=t.value)?void 0:p.trading_advice)?void 0:Q.stop_loss),O=T(null==(C=null==(h=t.value)?void 0:h.trading_advice)?void 0:C.take_profit),k=(null==(F=null==(U=t.value)?void 0:U.risk_assessment)?void 0:F.level)||"中",_=(null==(v=null==(y=t.value)?void 0:y.risk_assessment)?void 0:v.score)||50,R=(null==(b=null==(m=t.value)?void 0:m.risk_assessment)?void 0:b.details)||[];let P=`${E}市场分析报告 - 当前价格: ${H} USD\n\n本报告来源 - K线军师\n\n市场趋势分析:\n${K.substring(0,100)}${K.length>100?"...":""}\n\n交易建议:\n操作: ${L}\n入场价: ${S}\n止损价: ${M}\n目标价: ${O}\n原因: ${D.substring(0,80)}${D.length>80?"...":""}\n\n风险评估:\n风险等级: ${k}\n风险评分: ${_}/100\n${R.length>0?"主要风险因素:\n"+R.slice(0,2).map((A=>`- ${A}`)).join("\n"):""}\n\n#加密货币 #技术分析 #交易建议`;P.length>270&&(console.log("分享文本过长，进行裁剪。原长度:",P.length),P=`${E}市场分析报告 - 当前价格: ${H} USD\n\n市场趋势:\n${K.substring(0,50)}${K.length>50?"...":""}\n\n交易建议:\n操作: ${L}\n入场价: ${S}\n止损价: ${M}\n目标价: ${O}\n\n风险评估:\n风险等级: ${k}\n风险评分: ${_}/100\n\n#加密货币 #技术分析 #交易建议`);const V=`https://twitter.com/intent/tweet?text=${encodeURIComponent(P)}`;window.open(V,"_blank")}catch(E){}},j=async()=>{var A,e,r,n,s,o,i,l;try{const c=document.createElement("div");c.style.width="375px",c.style.padding="20px",c.style.backgroundColor="#0F172A",c.style.color="#fff",c.style.fontFamily="system-ui, -apple-system, sans-serif",c.style.position="fixed",c.style.left="-9999px",c.style.top="0",c.style.zIndex="-1";const B=document.createElement("div");if(B.style.textAlign="center",B.style.marginBottom="20px",B.style.padding="20px 0 10px 0",B.style.background="linear-gradient(to bottom, #1e293b99 60%, #0f172a99 100%)",B.style.borderRadius="16px",B.style.boxShadow="0 2px 8px 0 #0002",B.innerHTML=`\n      <h2 style="font-size: 22px; margin-bottom: 10px; font-weight: 600; letter-spacing: 1px;">${a.value} Market Analysis</h2>\n      <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">\n        ${T(null==(A=t.value)?void 0:A.current_price)} <span style='font-size:16px;color:#9ca3af'>USD</span>\n      </div>\n    `,c.appendChild(B),null==(r=null==(e=t.value)?void 0:e.trend_analysis)?void 0:r.summary){const A=document.createElement("div");A.style.margin="20px 0 0 0",A.style.padding="16px",A.style.background="rgba(31,41,55,0.3)",A.style.border="1px solid #374151",A.style.borderRadius="12px",A.style.boxShadow="0 1px 4px 0 #0001",A.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">Market Trend Analysis</div>\n        <div style="font-size: 14px; color: #d1d5db; line-height: 1.6; margin-bottom: 12px;">${t.value.trend_analysis.summary}</div>\n        <div style="display: flex; justify-content: center; gap: 8px;">\n          <div style="flex:1; text-align:center; background:rgba(16,185,129,0.12); border-radius:8px; padding:8px 0; border:1px solid #10b98133;">\n            <div style="color:#4ade80; font-size:18px; font-weight:600;">${P(t.value.trend_analysis.probabilities.up)}</div>\n            <div style="color:#4ade80; font-size:12px;">Up</div>\n          </div>\n          <div style="flex:1; text-align:center; background:rgba(156,163,175,0.12); border-radius:8px; padding:8px 0; border:1px solid #9ca3af33;">\n            <div style="color:#9ca3af; font-size:18px; font-weight:600;">${P(t.value.trend_analysis.probabilities.sideways)}</div>\n            <div style="color:#9ca3af; font-size:12px;">Sideways</div>\n          </div>\n          <div style="flex:1; text-align:center; background:rgba(239,68,68,0.12); border-radius:8px; padding:8px 0; border:1px solid #ef444433;">\n            <div style="color:#ef4444; font-size:18px; font-weight:600;">${P(t.value.trend_analysis.probabilities.down)}</div>\n            <div style="color:#ef4444; font-size:12px;">Down</div>\n          </div>\n        </div>\n      `,c.appendChild(A)}if(null==(n=t.value)?void 0:n.indicators_analysis){const A=document.createElement("div");A.style.margin="20px 0 0 0",A.style.padding="16px",A.style.background="rgba(31,41,55,0.3)",A.style.border="1px solid #374151",A.style.borderRadius="12px",A.style.boxShadow="0 1px 4px 0 #0001",A.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Technical Indicators</div>\n        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">\n          ${Object.entries(t.value.indicators_analysis).filter((([A])=>!["MACD","BollingerBands","DMI"].includes(A))).map((([A,e])=>`\n              <div style="padding: 10px; background: rgba(17,24,39,0.5); border: 1px solid #334155; border-radius: 8px;">\n                <div style="font-size: 12px; color: #9ca3af; margin-bottom: 5px;">${A}</div>\n                <div style="display: flex; justify-content: space-between; align-items: center;">\n                  <span style="font-size: 14px;">${"number"==typeof e.value?e.value.toFixed(2):e.value}</span>\n                  <span style="font-size: 12px;">${(A=>{const e="display:inline-flex;align-items:center;justify-content:center;width:20px;height:20px;border-radius:50%;",t="font-size:14px;line-height:1;height:14px;vertical-align:middle;display:block;margin-top:-14px;";return"bullish"===A||"看涨"===A||"支持当前趋势"===A?`<span style="${e}background:rgba(16,185,129,0.12);">\n      <i class='ri-arrow-up-line' style='${t}color:#22c55e;'></i>\n    </span>`:"bearish"===A||"看跌"===A||"反对当前趋势"===A?`<span style="${e}background:rgba(239,68,68,0.12);">\n      <i class='ri-arrow-down-line' style='${t}color:#ef4444;'></i>\n    </span>`:`<span style="${e}background:rgba(156,163,175,0.12);">\n    <i class='ri-subtract-line' style='${t}color:#9ca3af;'></i>\n  </span>`})(e.support_trend)}</span>\n                </div>\n              </div>\n            `)).join("")}\n        </div>\n      `,c.appendChild(A)}if(null==(s=t.value)?void 0:s.trading_advice){const A=t.value.trading_advice,e=document.createElement("div");e.style.margin="20px 0 0 0",e.style.padding="16px",e.style.background="rgba(31,41,55,0.3)",e.style.border="1px solid #374151",e.style.borderRadius="12px",e.style.boxShadow="0 1px 4px 0 #0001",e.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Trading Advice</div>\n        <div style="display: flex; flex-direction: column; gap: 6px; font-size: 14px;">\n          <div><span style='color:#9ca3af'>Action:</span> <span style='font-weight:500;'>${A.action}</span></div>\n          <div><span style='color:#9ca3af'>Entry Price:</span> ${T(A.entry_price)}</div>\n          <div><span style='color:#9ca3af'>Stop Loss:</span> <span style='color:#ef4444'>${T(A.stop_loss)}</span></div>\n          <div><span style='color:#9ca3af'>Take Profit:</span> <span style='color:#4ade80'>${T(A.take_profit)}</span></div>\n          <div><span style='color:#9ca3af'>Reason:</span> ${A.reason}</div>\n        </div>\n      `,c.appendChild(e)}if(null==(o=t.value)?void 0:o.risk_assessment){const A=t.value.risk_assessment,e=document.createElement("div");e.style.margin="20px 0 0 0",e.style.padding="16px",e.style.background="rgba(31,41,55,0.3)",e.style.border="1px solid #374151",e.style.borderRadius="12px",e.style.boxShadow="0 1px 4px 0 #0001",e.innerHTML=`\n        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Risk Assessment</div>\n        <div style="display: flex; flex-direction: column; gap: 6px; font-size: 14px;">\n          <div><span style='color:#9ca3af'>Level:</span> <span style='font-weight:500;'>${A.level}</span></div>\n          <div><span style='color:#9ca3af'>Score:</span> ${A.score}/100</div>\n          ${A.details&&A.details.length>0?`<div><span style='color:#9ca3af'>Factors:</span><ul style='margin:0 0 0 18px;padding:0;color:#d1d5db;'>${A.details.map((A=>`<li>${A}</li>`)).join("")}</ul></div>`:""}\n        </div>\n      `,c.appendChild(e)}const u=document.createElement("div");u.style.textAlign="center",u.style.margin="32px 0 0 0",u.style.padding="16px 0 0 0",u.style.display="flex",u.style.flexDirection="column",u.style.alignItems="center",u.innerHTML='\n      <div style="margin-bottom: 8px; font-size: 15px; color: #38bdf8; font-weight: 600;">K线军师</div>\n      <div style="margin-bottom: 10px; font-size: 13px; color: #9ca3af; max-width: 320px;">\n        智能加密行情分析与交易决策平台，助你高效洞察市场趋势，科学制定交易策略。\n      </div>\n    ';const d=document.createElement("canvas");u.appendChild(d);const g=document.createElement("div");g.style.marginTop="10px",g.style.fontSize="14px",g.style.color="#60a5fa",g.style.fontWeight="bold",g.innerText="https://www.kxianjunshi.com",u.appendChild(g),c.appendChild(u),document.body.appendChild(c),await Pi.toCanvas(d,"https://www.kxianjunshi.com",{width:100,margin:1});const w=await(i=c,l={backgroundColor:"#0F172A",scale:2,logging:!1,width:375,height:c.offsetHeight,onclone:function(A){const e=A.body.querySelector("div");e&&window.getComputedStyle(e).getPropertyValue("height")}},void 0===l&&(l={}),_i(i,l));document.body.removeChild(c);const f=document.createElement("a");f.download=`${a.value}_market_analysis.png`,f.href=w.toDataURL("image/png"),f.click()}catch(c){console.error("保存图片失败:",c),ww({message:"保存图片失败，请重试",type:"error"})}},z=A=>"bullish"===A||"看涨"===A||"支持当前趋势"===A?"rgba(16,185,129,0.12)":"bearish"===A||"看跌"===A||"反对当前趋势"===A?"rgba(239,68,68,0.12)":"rgba(156,163,175,0.12)";return(A,e)=>{var r,o,l,u,d,g,p,R;const G=tA("router-link");return y(),F("div",Ew,[b("header",Hw,[b("div",Iw,[b("div",xw,[b("h1",Kw,Y(a.value?`${$=a.value,$.replace("USDT","")}市场分析报告`:"加载中..."),1),b("div",Lw,[b("span",Dw,"更新时间: "+Y(O(null==(r=t.value)?void 0:r.last_update_time)),1),b("button",{class:I(["w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-800 transition",{"opacity-50 cursor-not-allowed":B.value}]),onClick:J,disabled:B.value},[b("i",{class:I(["ri-refresh-line ri-lg",{"animate-spin":B.value}])},null,2)],10,Sw)])])])]),b("main",Tw,[n.value&&!t.value?(y(),F("div",Mw,e[0]||(e[0]=[AA('<div class="mt-6 p-5 rounded-lg bg-gradient-to-b from-gray-800/60 to-gray-900/60 border border-gray-700/50 shadow-lg animate-pulse" data-v-30f0ce7b><div class="h-5 w-32 bg-gray-700/50 rounded mx-auto mb-2" data-v-30f0ce7b></div><div class="h-8 w-40 bg-gray-700/50 rounded mx-auto mb-4" data-v-30f0ce7b></div><div class="flex justify-center gap-3 mt-4 mb-2" data-v-30f0ce7b><div class="h-8 w-24 bg-gray-800/70 rounded-full" data-v-30f0ce7b></div><div class="h-8 w-24 bg-gray-800/70 rounded-full" data-v-30f0ce7b></div></div></div><div class="mt-6 grid grid-cols-3 gap-3" data-v-30f0ce7b><div class="p-3 rounded-lg bg-gradient-to-br from-green-600/20 to-green-800/20 border border-green-500/30 animate-pulse" data-v-30f0ce7b><div class="h-6 w-16 bg-green-500/20 rounded mx-auto mb-1" data-v-30f0ce7b></div><div class="h-4 w-12 bg-green-500/20 rounded mx-auto" data-v-30f0ce7b></div></div><div class="p-3 rounded-lg bg-gradient-to-br from-gray-700/20 to-gray-800/20 border border-gray-600/30 animate-pulse" data-v-30f0ce7b><div class="h-6 w-16 bg-gray-600/20 rounded mx-auto mb-1" data-v-30f0ce7b></div><div class="h-4 w-12 bg-gray-600/20 rounded mx-auto" data-v-30f0ce7b></div></div><div class="p-3 rounded-lg bg-[rgba(239,68,68,0.12)] border border-red-500/30 animate-pulse" data-v-30f0ce7b><div class="h-6 w-16 bg-red-500/20 rounded mx-auto mb-1" data-v-30f0ce7b></div><div class="h-4 w-12 bg-red-500/20 rounded mx-auto" data-v-30f0ce7b></div></div></div><div class="mt-6" data-v-30f0ce7b><div class="h-6 w-32 bg-gray-700/50 rounded mb-3" data-v-30f0ce7b></div><div class="p-4 rounded-lg bg-gray-800/30 border border-gray-700/50" data-v-30f0ce7b><div class="h-4 w-full bg-gray-700/50 rounded mb-2" data-v-30f0ce7b></div><div class="h-4 w-3/4 bg-gray-700/50 rounded" data-v-30f0ce7b></div></div></div><div class="mt-6" data-v-30f0ce7b><div class="h-6 w-32 bg-gray-700/50 rounded mb-3" data-v-30f0ce7b></div><div class="grid grid-cols-2 gap-3" data-v-30f0ce7b><div class="p-3 rounded-lg bg-gray-800/30 border border-gray-700/50" data-v-30f0ce7b><div class="h-4 w-16 bg-gray-700/50 rounded mb-2" data-v-30f0ce7b></div><div class="h-6 w-full bg-gray-700/50 rounded" data-v-30f0ce7b></div></div><div class="p-3 rounded-lg bg-gray-800/30 border border-gray-700/50" data-v-30f0ce7b><div class="h-4 w-16 bg-gray-700/50 rounded mb-2" data-v-30f0ce7b></div><div class="h-6 w-full bg-gray-700/50 rounded" data-v-30f0ce7b></div></div></div></div>',4)]))):c.value?(y(),F("div",Ow,[M(bw,{symbol:a.value,onRefresh:J,"is-refreshing":B.value},null,8,["symbol","is-refreshing"])])):i.value?(y(),F("div",kw,[b("div",_w,[e[1]||(e[1]=b("i",{class:"ri-error-warning-line text-4xl text-red-500 mb-2"},null,-1)),b("p",Rw,Y(i.value),1),e[2]||(e[2]=b("p",{class:"text-gray-400 text-sm mb-4"},"请尝试重新加载或稍后再试",-1)),b("div",{class:"flex space-x-3 justify-center"},[b("button",{class:"px-4 py-2 bg-primary text-white rounded-lg",onClick:X}," 重试 "),b("button",{class:"px-4 py-2 bg-blue-600 text-white rounded-lg",onClick:J}," 强制刷新 ")])])])):t.value?(y(),F("div",Pw,[b("div",Vw,[e[6]||(e[6]=b("h2",{class:"text-center text-gray-400 mb-1"},"快照价格",-1)),b("div",Nw,[W(Y(T(t.value.snapshot_price))+" ",1),e[3]||(e[3]=b("span",{class:"text-sm text-gray-400"},"USD",-1))]),b("div",{class:"flex justify-center gap-3 mt-4 mb-2"},[b("button",{class:"px-4 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 rounded-full transition flex items-center gap-1",onClick:Z},e[4]||(e[4]=[b("i",{class:"ri-twitter-fill"},null,-1),b("span",{class:"text-sm"},"分享到推特",-1)])),b("button",{class:"px-4 py-2 bg-gray-600/20 hover:bg-gray-600/30 text-gray-400 rounded-full transition flex items-center gap-1",onClick:j},e[5]||(e[5]=[b("i",{class:"ri-image-line"},null,-1),b("span",{class:"text-sm"},"保存图片",-1)]))])]),(null==(l=null==(o=t.value)?void 0:o.trend_analysis)?void 0:l.probabilities)?(y(),F("div",Gw,[b("div",Jw,[b("div",Xw,Y(P(t.value.trend_analysis.probabilities.up)),1),e[7]||(e[7]=b("div",{class:"text-xs text-green-300 flex items-center justify-center"},[b("i",{class:"ri-arrow-up-line w-4 h-4 flex items-center justify-center"}),b("span",null,"上涨趋势")],-1))]),b("div",Yw,[b("div",Ww,Y(P(t.value.trend_analysis.probabilities.sideways)),1),e[8]||(e[8]=b("div",{class:"text-xs text-gray-400 flex items-center justify-center"},[b("i",{class:"ri-subtract-line w-4 h-4 flex items-center justify-center"}),b("span",null,"横盘整理")],-1))]),b("div",Zw,[b("div",jw,Y(P(t.value.trend_analysis.probabilities.down)),1),e[9]||(e[9]=b("div",{class:"text-xs text-red-300 flex items-center justify-center"},[b("i",{class:"ri-arrow-down-line w-4 h-4 flex items-center justify-center"}),b("span",null,"下跌趋势")],-1))])])):k("",!0),(null==(d=null==(u=t.value)?void 0:u.trend_analysis)?void 0:d.summary)?(y(),F("div",zw,[e[10]||(e[10]=b("h3",{class:"text-lg font-medium mb-3"},"市场趋势分析",-1)),b("div",$w,[b("p",qw,Y(t.value.trend_analysis.summary),1)])])):k("",!0),(null==(g=t.value)?void 0:g.indicators_analysis)?(y(),F("div",Af,[e[42]||(e[42]=b("h3",{class:"text-lg font-medium mb-3"},"技术指标",-1)),b("div",ef,[b("div",tf,[b("div",rf,[b("div",nf,[e[12]||(e[12]=W(" RSI (14) ")),M(s($g),{content:Q,placement:"top"},{default:_((()=>e[11]||(e[11]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",sf,[b("span",of,Y(t.value.indicators_analysis.RSI.value),1),b("span",{class:I([V(t.value.indicators_analysis.RSI.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.RSI.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.RSI.support_trend),"text-base"])},null,2)],6)])]),b("div",af,[b("div",lf,[e[14]||(e[14]=W(" BIAS ")),M(s($g),{content:h,placement:"top"},{default:_((()=>e[13]||(e[13]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",cf,[b("span",Bf,Y(t.value.indicators_analysis.BIAS.value),1),b("span",{class:I([V(t.value.indicators_analysis.BIAS.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.BIAS.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.BIAS.support_trend),"text-base"])},null,2)],6)])]),b("div",uf,[b("div",df,[e[16]||(e[16]=W(" PSY ")),M(s($g),{content:C,placement:"top"},{default:_((()=>e[15]||(e[15]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",gf,[b("span",wf,Y(t.value.indicators_analysis.PSY.value),1),b("span",{class:I([V(t.value.indicators_analysis.PSY.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.PSY.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.PSY.support_trend),"text-base"])},null,2)],6)])]),b("div",ff,[b("div",pf,[e[18]||(e[18]=W(" VWAP ")),M(s($g),{content:U,placement:"top"},{default:_((()=>e[17]||(e[17]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",Qf,[b("span",hf,Y(t.value.indicators_analysis.VWAP.value.toFixed(2)),1),b("span",{class:I([V(t.value.indicators_analysis.VWAP.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.VWAP.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.VWAP.support_trend),"text-base"])},null,2)],6)])]),b("div",Cf,[b("div",Uf,[e[20]||(e[20]=W(" Funding Rate ")),M(s($g),{content:v,placement:"top"},{default:_((()=>e[19]||(e[19]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",Ff,[b("span",yf,Y((100*t.value.indicators_analysis.FundingRate.value).toFixed(4))+"%",1),b("span",{class:I([V(t.value.indicators_analysis.FundingRate.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.FundingRate.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.FundingRate.support_trend),"text-base"])},null,2)],6)])]),b("div",vf,[b("div",mf,[e[22]||(e[22]=W(" Exchange Netflow ")),M(s($g),{content:m,placement:"top"},{default:_((()=>e[21]||(e[21]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",bf,[b("span",Ef,Y(t.value.indicators_analysis.ExchangeNetflow.value.toFixed(2)),1),b("span",{class:I([V(t.value.indicators_analysis.ExchangeNetflow.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.ExchangeNetflow.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.ExchangeNetflow.support_trend),"text-base"])},null,2)],6)])]),b("div",Hf,[b("div",If,[e[24]||(e[24]=W(" NUPL ")),M(s($g),{content:E,placement:"top"},{default:_((()=>e[23]||(e[23]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",xf,[b("span",Kf,Y(t.value.indicators_analysis.NUPL.value.toFixed(2)),1),b("span",{class:I([V(t.value.indicators_analysis.NUPL.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.NUPL.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.NUPL.support_trend),"text-base"])},null,2)],6)])]),b("div",Lf,[b("div",Df,[e[26]||(e[26]=W(" Mayer Multiple ")),M(s($g),{content:x,placement:"top"},{default:_((()=>e[25]||(e[25]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("div",Sf,[b("span",Tf,Y(t.value.indicators_analysis.MayerMultiple.value.toFixed(2)),1),b("span",{class:I([V(t.value.indicators_analysis.MayerMultiple.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.MayerMultiple.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.MayerMultiple.support_trend),"text-base"])},null,2)],6)])])]),b("div",Mf,[b("div",Of,[b("div",kf,[e[28]||(e[28]=W(" MACD ")),M(s($g),{content:K,placement:"top"},{default:_((()=>e[27]||(e[27]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("span",{class:I([V(t.value.indicators_analysis.MACD.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.MACD.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.MACD.support_trend),"text-base"])},null,2)],6)]),b("div",_f,[b("div",Rf,[e[29]||(e[29]=b("div",{class:"text-xs text-gray-400"},"Histogram",-1)),b("div",Pf,Y(t.value.indicators_analysis.MACD.value.histogram.toFixed(2)),1)]),b("div",Vf,[e[30]||(e[30]=b("div",{class:"text-xs text-gray-400"},"MACD Line",-1)),b("div",Nf,Y(t.value.indicators_analysis.MACD.value.line.toFixed(2)),1)]),b("div",Gf,[e[31]||(e[31]=b("div",{class:"text-xs text-gray-400"},"Signal Line",-1)),b("div",Jf,Y(t.value.indicators_analysis.MACD.value.signal.toFixed(2)),1)])])]),b("div",Xf,[b("div",Yf,[b("div",Wf,[e[33]||(e[33]=W(" Bollinger Bands ")),M(s($g),{content:L,placement:"top"},{default:_((()=>e[32]||(e[32]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("span",{class:I([V(t.value.indicators_analysis.BollingerBands.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.BollingerBands.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.BollingerBands.support_trend),"text-base"])},null,2)],6)]),b("div",Zf,[b("div",jf,[e[34]||(e[34]=b("div",{class:"text-xs text-gray-400"},"Upper Band",-1)),b("div",zf,Y(t.value.indicators_analysis.BollingerBands.value.upper.toFixed(2)),1)]),b("div",$f,[e[35]||(e[35]=b("div",{class:"text-xs text-gray-400"},"Middle Band",-1)),b("div",qf,Y(t.value.indicators_analysis.BollingerBands.value.middle.toFixed(2)),1)]),b("div",Ap,[e[36]||(e[36]=b("div",{class:"text-xs text-gray-400"},"Lower Band",-1)),b("div",ep,Y(t.value.indicators_analysis.BollingerBands.value.lower.toFixed(2)),1)])])]),b("div",tp,[b("div",rp,[b("div",np,[e[38]||(e[38]=W(" DMI ")),M(s($g),{content:S,placement:"top"},{default:_((()=>e[37]||(e[37]=[b("i",{class:"ri-question-line cursor-help"},null,-1)]))),_:1},8,["content"])]),b("span",{class:I([V(t.value.indicators_analysis.DMI.support_trend),"text-xs flex items-center justify-center w-5 h-5 rounded-full"]),style:H(`background:${z(t.value.indicators_analysis.DMI.support_trend)}`)},[b("i",{class:I([N(t.value.indicators_analysis.DMI.support_trend),"text-base"])},null,2)],6)]),b("div",sp,[b("div",op,[e[39]||(e[39]=b("div",{class:"text-xs text-gray-400"},"+DI",-1)),b("div",ip,Y(t.value.indicators_analysis.DMI.value.plus_di.toFixed(2)),1)]),b("div",ap,[e[40]||(e[40]=b("div",{class:"text-xs text-gray-400"},"-DI",-1)),b("div",lp,Y(t.value.indicators_analysis.DMI.value.minus_di.toFixed(2)),1)]),b("div",cp,[e[41]||(e[41]=b("div",{class:"text-xs text-gray-400"},"ADX",-1)),b("div",Bp,Y(t.value.indicators_analysis.DMI.value.adx.toFixed(2)),1)])])])])])):k("",!0),(null==(p=t.value)?void 0:p.trading_advice)?(y(),F("div",up,[e[48]||(e[48]=b("h3",{class:"text-lg font-medium mb-3"},"交易建议",-1)),b("div",dp,[b("div",gp,[e[43]||(e[43]=b("div",{class:"text-sm text-gray-400"},"建议操作",-1)),b("div",{class:I(["text-sm","买入"===t.value.trading_advice.action?"text-green-400":"卖出"===t.value.trading_advice.action?"text-red-400":"text-gray-400"])},Y(t.value.trading_advice.action),3)]),b("div",wp,[e[44]||(e[44]=b("div",{class:"text-sm text-gray-400"},"入场价格",-1)),b("div",fp,Y(T(t.value.trading_advice.entry_price)),1)]),b("div",pp,[e[45]||(e[45]=b("div",{class:"text-sm text-gray-400"},"止损价格",-1)),b("div",Qp,Y(T(t.value.trading_advice.stop_loss)),1)]),b("div",hp,[e[46]||(e[46]=b("div",{class:"text-sm text-gray-400"},"目标价格",-1)),b("div",Cp,Y(T(t.value.trading_advice.take_profit)),1)]),b("div",Up,[e[47]||(e[47]=b("div",{class:"text-sm text-gray-400 mb-1"},"原因分析",-1)),b("div",Fp,Y(t.value.trading_advice.reason),1)])])])):k("",!0),(null==(R=t.value)?void 0:R.risk_assessment)?(y(),F("div",yp,[e[52]||(e[52]=b("h3",{class:"text-lg font-medium mb-3"},"风险评估",-1)),b("div",vp,[b("div",mp,[e[49]||(e[49]=b("div",{class:"text-sm text-gray-400"},"风险等级",-1)),b("div",{class:I(["px-2 py-0.5 rounded",{"bg-red-900/30 text-red-400":"高"===t.value.risk_assessment.level,"bg-yellow-900/30 text-yellow-400":"中"===t.value.risk_assessment.level,"bg-green-900/30 text-green-400":"低"===t.value.risk_assessment.level}])},Y(t.value.risk_assessment.level),3)]),b("div",bp,[e[50]||(e[50]=b("div",{class:"text-sm text-gray-400 mb-1"},"风险评分",-1)),b("div",Ep,[b("div",{class:I(["h-2 rounded-full",{"bg-red-500":t.value.risk_assessment.score>70,"bg-yellow-500":t.value.risk_assessment.score>30&&t.value.risk_assessment.score<=70,"bg-green-500":t.value.risk_assessment.score<=30}]),style:H({width:`${t.value.risk_assessment.score}%`})},null,6)])]),t.value.risk_assessment.details&&t.value.risk_assessment.details.length>0?(y(),F("div",Hp,[e[51]||(e[51]=b("div",{class:"text-sm text-gray-400 mb-1"},"风险因素",-1)),b("ul",Ip,[(y(!0),F(D,null,eA(t.value.risk_assessment.details,((A,e)=>(y(),F("li",{key:e},Y(A),1)))),128))])])):k("",!0)])])):k("",!0)])):k("",!0)]),B.value?(y(),F("div",xp,[b("div",Kp,[e[53]||(e[53]=b("h3",{class:"text-lg font-medium text-center mb-4"},"正在刷新数据",-1)),b("div",Lp,[b("div",{class:"absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-primary rounded-full transition-all duration-300",style:H({width:`${w.value}%`})},null,4)]),b("div",Dp,Y(Math.round(w.value))+"% ",1),b("p",Sp,Y(f.value),1)])])):k("",!0),b("nav",Tp,[b("div",Mp,[b("div",Op,[M(G,{to:"/",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:_((()=>e[54]||(e[54]=[b("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1),b("span",{class:"text-xs mt-0.5"},"行情",-1)]))),_:1}),M(G,{to:"/profile",class:"flex flex-col items-center justify-center text-gray-500"},{default:_((()=>e[55]||(e[55]=[b("i",{class:"ri-user-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1),b("span",{class:"text-xs mt-0.5"},"我的",-1)]))),_:1})])])])]);var $}}}),_p=rA(kp,[["__scopeId","data-v-30f0ce7b"]]);export{_p as default};
