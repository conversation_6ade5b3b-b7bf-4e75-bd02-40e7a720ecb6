function e(e,t){return function(){return e.apply(t,arguments)}}const{toString:t}=Object.prototype,{getPrototypeOf:r}=Object,{iterator:n,toStringTag:o}=Symbol,s=(e=>r=>{const n=t.call(r);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),i=e=>(e=e.toLowerCase(),t=>s(t)===e),a=e=>t=>typeof t===e,{isArray:c}=Array,l=a("undefined");const u=i("ArrayBuffer");const d=a("string"),f=a("function"),h=a("number"),p=e=>null!==e&&"object"==typeof e,m=e=>{if("object"!==s(e))return!1;const t=r(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||o in e||n in e)},g=i("Date"),y=i("File"),w=i("Blob"),b=i("FileList"),E=i("URLSearchParams"),[R,O,S,T]=["ReadableStream","Request","Response","Headers"].map(i);function v(e,t,{allOwnKeys:r=!1}={}){if(null==e)return;let n,o;if("object"!=typeof e&&(e=[e]),c(e))for(n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(n=0;n<s;n++)i=o[n],t.call(null,e[i],i,e)}}function A(e,t){t=t.toLowerCase();const r=Object.keys(e);let n,o=r.length;for(;o-- >0;)if(n=r[o],t===n.toLowerCase())return n;return null}const C="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,j=e=>!l(e)&&e!==C;const _=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&r(Uint8Array)),P=i("HTMLFormElement"),N=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),x=i("RegExp"),U=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};v(r,((r,o)=>{let s;!1!==(s=t(r,o,e))&&(n[o]=s||r)})),Object.defineProperties(e,n)};const k=i("AsyncFunction"),F=(L="function"==typeof setImmediate,D=f(C.postMessage),L?setImmediate:D?(B=`axios@${Math.random()}`,I=[],C.addEventListener("message",(({source:e,data:t})=>{e===C&&t===B&&I.length&&I.shift()()}),!1),e=>{I.push(e),C.postMessage(B,"*")}):e=>setTimeout(e));var L,D,B,I;const q="undefined"!=typeof queueMicrotask?queueMicrotask.bind(C):"undefined"!=typeof process&&process.nextTick||F,z={isArray:c,isArrayBuffer:u,isBuffer:function(e){return null!==e&&!l(e)&&null!==e.constructor&&!l(e.constructor)&&f(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||f(e.append)&&("formdata"===(t=s(e))||"object"===t&&f(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&u(e.buffer),t},isString:d,isNumber:h,isBoolean:e=>!0===e||!1===e,isObject:p,isPlainObject:m,isReadableStream:R,isRequest:O,isResponse:S,isHeaders:T,isUndefined:l,isDate:g,isFile:y,isBlob:w,isRegExp:x,isFunction:f,isStream:e=>p(e)&&f(e.pipe),isURLSearchParams:E,isTypedArray:_,isFileList:b,forEach:v,merge:function e(){const{caseless:t}=j(this)&&this||{},r={},n=(n,o)=>{const s=t&&A(r,o)||o;m(r[s])&&m(n)?r[s]=e(r[s],n):m(n)?r[s]=e({},n):c(n)?r[s]=n.slice():r[s]=n};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&v(arguments[o],n);return r},extend:(t,r,n,{allOwnKeys:o}={})=>(v(r,((r,o)=>{n&&f(r)?t[o]=e(r,n):t[o]=r}),{allOwnKeys:o}),t),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,n,o)=>{let s,i,a;const c={};if(t=t||{},null==e)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)a=s[i],o&&!o(a,e,t)||c[a]||(t[a]=e[a],c[a]=!0);e=!1!==n&&r(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:s,kindOfTest:i,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return-1!==n&&n===r},toArray:e=>{if(!e)return null;if(c(e))return e;let t=e.length;if(!h(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{const r=(e&&e[n]).call(e);let o;for(;(o=r.next())&&!o.done;){const r=o.value;t.call(e,r[0],r[1])}},matchAll:(e,t)=>{let r;const n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:P,hasOwnProperty:N,hasOwnProp:N,reduceDescriptors:U,freezeMethods:e=>{U(e,((t,r)=>{if(f(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;const n=e[r];f(n)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")}))}))},toObjectSet:(e,t)=>{const r={},n=e=>{e.forEach((e=>{r[e]=!0}))};return c(e)?n(e):n(String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,r){return t.toUpperCase()+r})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:A,global:C,isContextDefined:j,isSpecCompliantForm:function(e){return!!(e&&f(e.append)&&"FormData"===e[o]&&e[n])},toJSONObject:e=>{const t=new Array(10),r=(e,n)=>{if(p(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;const o=c(e)?[]:{};return v(e,((e,t)=>{const s=r(e,n+1);!l(s)&&(o[t]=s)})),t[n]=void 0,o}}return e};return r(e,0)},isAsyncFn:k,isThenable:e=>e&&(p(e)||f(e))&&f(e.then)&&f(e.catch),setImmediate:F,asap:q,isIterable:e=>null!=e&&f(e[n])};function M(e,t,r,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}z.inherits(M,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const $=M.prototype,W={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{W[e]={value:e}})),Object.defineProperties(M,W),Object.defineProperty($,"isAxiosError",{value:!0}),M.from=(e,t,r,n,o,s)=>{const i=Object.create($);return z.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),M.call(i,e.message,t,r,n,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function H(e){return z.isPlainObject(e)||z.isArray(e)}function J(e){return z.endsWith(e,"[]")?e.slice(0,-2):e}function K(e,t,r){return e?e.concat(t).map((function(e,t){return e=J(e),!r&&t?"["+e+"]":e})).join(r?".":""):t}const V=z.toFlatObject(z,{},null,(function(e){return/^is[A-Z]/.test(e)}));function X(e,t,r){if(!z.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const n=(r=z.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!z.isUndefined(t[e])}))).metaTokens,o=r.visitor||l,s=r.dots,i=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&z.isSpecCompliantForm(t);if(!z.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(z.isDate(e))return e.toISOString();if(!a&&z.isBlob(e))throw new M("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(e)||z.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,r,o){let a=e;if(e&&!o&&"object"==typeof e)if(z.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else if(z.isArray(e)&&function(e){return z.isArray(e)&&!e.some(H)}(e)||(z.isFileList(e)||z.endsWith(r,"[]"))&&(a=z.toArray(e)))return r=J(r),a.forEach((function(e,n){!z.isUndefined(e)&&null!==e&&t.append(!0===i?K([r],n,s):null===i?r:r+"[]",c(e))})),!1;return!!H(e)||(t.append(K(o,r,s),c(e)),!1)}const u=[],d=Object.assign(V,{defaultVisitor:l,convertValue:c,isVisitable:H});if(!z.isObject(e))throw new TypeError("data must be an object");return function e(r,n){if(!z.isUndefined(r)){if(-1!==u.indexOf(r))throw Error("Circular reference detected in "+n.join("."));u.push(r),z.forEach(r,(function(r,s){!0===(!(z.isUndefined(r)||null===r)&&o.call(t,r,z.isString(s)?s.trim():s,n,d))&&e(r,n?n.concat(s):[s])})),u.pop()}}(e),t}function G(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Q(e,t){this._pairs=[],e&&X(e,this,t)}const Y=Q.prototype;function Z(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ee(e,t,r){if(!t)return e;const n=r&&r.encode||Z;z.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let s;if(s=o?o(t,r):z.isURLSearchParams(t)?t.toString():new Q(t,r).toString(n),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}Y.append=function(e,t){this._pairs.push([e,t])},Y.toString=function(e){const t=e?function(t){return e.call(this,t,G)}:G;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class te{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){z.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const re={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ne={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Q,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},oe="undefined"!=typeof window&&"undefined"!=typeof document,se="object"==typeof navigator&&navigator||void 0,ie=oe&&(!se||["ReactNative","NativeScript","NS"].indexOf(se.product)<0),ae="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ce=oe&&window.location.href||"http://localhost",le={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:oe,hasStandardBrowserEnv:ie,hasStandardBrowserWebWorkerEnv:ae,navigator:se,origin:ce},Symbol.toStringTag,{value:"Module"})),...ne};function ue(e){function t(e,r,n,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&z.isArray(n)?n.length:s,a)return z.hasOwnProp(n,s)?n[s]=[n[s],r]:n[s]=r,!i;n[s]&&z.isObject(n[s])||(n[s]=[]);return t(e,r,n[s],o)&&z.isArray(n[s])&&(n[s]=function(e){const t={},r=Object.keys(e);let n;const o=r.length;let s;for(n=0;n<o;n++)s=r[n],t[s]=e[s];return t}(n[s])),!i}if(z.isFormData(e)&&z.isFunction(e.entries)){const r={};return z.forEachEntry(e,((e,n)=>{t(function(e){return z.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),n,r,0)})),r}return null}const de={transitional:re,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const r=t.getContentType()||"",n=r.indexOf("application/json")>-1,o=z.isObject(e);o&&z.isHTMLForm(e)&&(e=new FormData(e));if(z.isFormData(e))return n?JSON.stringify(ue(e)):e;if(z.isArrayBuffer(e)||z.isBuffer(e)||z.isStream(e)||z.isFile(e)||z.isBlob(e)||z.isReadableStream(e))return e;if(z.isArrayBufferView(e))return e.buffer;if(z.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return X(e,new le.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return le.isNode&&z.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=z.isFileList(e))||r.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return X(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||n?(t.setContentType("application/json",!1),function(e,t,r){if(z.isString(e))try{return(t||JSON.parse)(e),z.trim(e)}catch(n){if("SyntaxError"!==n.name)throw n}return(r||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||de.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(z.isResponse(e)||z.isReadableStream(e))return e;if(e&&z.isString(e)&&(r&&!this.responseType||n)){const r=!(t&&t.silentJSONParsing)&&n;try{return JSON.parse(e)}catch(o){if(r){if("SyntaxError"===o.name)throw M.from(o,M.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:le.classes.FormData,Blob:le.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],(e=>{de.headers[e]={}}));const fe=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),he=Symbol("internals");function pe(e){return e&&String(e).trim().toLowerCase()}function me(e){return!1===e||null==e?e:z.isArray(e)?e.map(me):String(e)}function ge(e,t,r,n,o){return z.isFunction(n)?n.call(this,t,r):(o&&(t=r),z.isString(t)?z.isString(n)?-1!==t.indexOf(n):z.isRegExp(n)?n.test(t):void 0:void 0)}let ye=class{constructor(e){e&&this.set(e)}set(e,t,r){const n=this;function o(e,t,r){const o=pe(t);if(!o)throw new Error("header name must be a non-empty string");const s=z.findKey(n,o);(!s||void 0===n[s]||!0===r||void 0===r&&!1!==n[s])&&(n[s||t]=me(e))}const s=(e,t)=>z.forEach(e,((e,r)=>o(e,r,t)));if(z.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(z.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let r,n,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),r=e.substring(0,o).trim().toLowerCase(),n=e.substring(o+1).trim(),!r||t[r]&&fe[r]||("set-cookie"===r?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)})),t})(e),t);else if(z.isObject(e)&&z.isIterable(e)){let r,n,o={};for(const t of e){if(!z.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[n=t[0]]=(r=o[n])?z.isArray(r)?[...r,t[1]]:[r,t[1]]:t[1]}s(o,t)}else null!=e&&o(t,e,r);return this}get(e,t){if(e=pe(e)){const r=z.findKey(this,e);if(r){const e=this[r];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}(e);if(z.isFunction(t))return t.call(this,e,r);if(z.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=pe(e)){const r=z.findKey(this,e);return!(!r||void 0===this[r]||t&&!ge(0,this[r],r,t))}return!1}delete(e,t){const r=this;let n=!1;function o(e){if(e=pe(e)){const o=z.findKey(r,e);!o||t&&!ge(0,r[o],o,t)||(delete r[o],n=!0)}}return z.isArray(e)?e.forEach(o):o(e),n}clear(e){const t=Object.keys(this);let r=t.length,n=!1;for(;r--;){const o=t[r];e&&!ge(0,this[o],o,e,!0)||(delete this[o],n=!0)}return n}normalize(e){const t=this,r={};return z.forEach(this,((n,o)=>{const s=z.findKey(r,o);if(s)return t[s]=me(n),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,r)=>t.toUpperCase()+r))}(o):String(o).trim();i!==o&&delete t[o],t[i]=me(n),r[i]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return z.forEach(this,((r,n)=>{null!=r&&!1!==r&&(t[n]=e&&z.isArray(r)?r.join(", "):r)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const r=new this(e);return t.forEach((e=>r.set(e))),r}static accessor(e){const t=(this[he]=this[he]={accessors:{}}).accessors,r=this.prototype;function n(e){const n=pe(e);t[n]||(!function(e,t){const r=z.toCamelCase(" "+t);["get","set","has"].forEach((n=>{Object.defineProperty(e,n+r,{value:function(e,r,o){return this[n].call(this,t,e,r,o)},configurable:!0})}))}(r,e),t[n]=!0)}return z.isArray(e)?e.forEach(n):n(e),this}};function we(e,t){const r=this||de,n=t||r,o=ye.from(n.headers);let s=n.data;return z.forEach(e,(function(e){s=e.call(r,s,o.normalize(),t?t.status:void 0)})),o.normalize(),s}function be(e){return!(!e||!e.__CANCEL__)}function Ee(e,t,r){M.call(this,null==e?"canceled":e,M.ERR_CANCELED,t,r),this.name="CanceledError"}function Re(e,t,r){const n=r.config.validateStatus;r.status&&n&&!n(r.status)?t(new M("Request failed with status code "+r.status,[M.ERR_BAD_REQUEST,M.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):e(r)}ye.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),z.reduceDescriptors(ye.prototype,(({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}})),z.freezeMethods(ye),z.inherits(Ee,M,{__CANCEL__:!0});const Oe=(e,t,r=3)=>{let n=0;const o=function(e,t){e=e||10;const r=new Array(e),n=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=n[i];o||(o=c),r[s]=a,n[s]=c;let u=i,d=0;for(;u!==s;)d+=r[u++],u%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const f=l&&c-l;return f?Math.round(1e3*d/f):void 0}}(50,250);return function(e,t){let r,n,o=0,s=1e3/t;const i=(t,s=Date.now())=>{o=s,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=s?i(e,t):(r=e,n||(n=setTimeout((()=>{n=null,i(r)}),s-a)))},()=>r&&i(r)]}((r=>{const s=r.loaded,i=r.lengthComputable?r.total:void 0,a=s-n,c=o(a);n=s;e({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:r,lengthComputable:null!=i,[t?"download":"upload"]:!0})}),r)},Se=(e,t)=>{const r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Te=e=>(...t)=>z.asap((()=>e(...t))),ve=le.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,le.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(le.origin),le.navigator&&/(msie|trident)/i.test(le.navigator.userAgent)):()=>!0,Ae=le.hasStandardBrowserEnv?{write(e,t,r,n,o,s){const i=[e+"="+encodeURIComponent(t)];z.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),z.isString(n)&&i.push("path="+n),z.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ce(e,t,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(n||0==r)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const je=e=>e instanceof ye?{...e}:e;function _e(e,t){t=t||{};const r={};function n(e,t,r,n){return z.isPlainObject(e)&&z.isPlainObject(t)?z.merge.call({caseless:n},e,t):z.isPlainObject(t)?z.merge({},t):z.isArray(t)?t.slice():t}function o(e,t,r,o){return z.isUndefined(t)?z.isUndefined(e)?void 0:n(void 0,e,0,o):n(e,t,0,o)}function s(e,t){if(!z.isUndefined(t))return n(void 0,t)}function i(e,t){return z.isUndefined(t)?z.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function a(r,o,s){return s in t?n(r,o):s in e?n(void 0,r):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t,r)=>o(je(e),je(t),0,!0)};return z.forEach(Object.keys(Object.assign({},e,t)),(function(n){const s=c[n]||o,i=s(e[n],t[n],n);z.isUndefined(i)&&s!==a||(r[n]=i)})),r}const Pe=e=>{const t=_e({},e);let r,{data:n,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:c}=t;if(t.headers=a=ye.from(a),t.url=ee(Ce(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),z.isFormData(n))if(le.hasStandardBrowserEnv||le.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(r=a.getContentType())){const[e,...t]=r?r.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(le.hasStandardBrowserEnv&&(o&&z.isFunction(o)&&(o=o(t)),o||!1!==o&&ve(t.url))){const e=s&&i&&Ae.read(i);e&&a.set(s,e)}return t},Ne="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,r){const n=Pe(e);let o=n.data;const s=ye.from(n.headers).normalize();let i,a,c,l,u,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=n;function p(){l&&l(),u&&u(),n.cancelToken&&n.cancelToken.unsubscribe(i),n.signal&&n.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const n=ye.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Re((function(e){t(e),p()}),(function(e){r(e),p()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:n,config:e,request:m}),m=null}m.open(n.method.toUpperCase(),n.url,!0),m.timeout=n.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(r(new M("Request aborted",M.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new M("Network Error",M.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const o=n.transitional||re;n.timeoutErrorMessage&&(t=n.timeoutErrorMessage),r(new M(t,o.clarifyTimeoutError?M.ETIMEDOUT:M.ECONNABORTED,e,m)),m=null},void 0===o&&s.setContentType(null),"setRequestHeader"in m&&z.forEach(s.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),z.isUndefined(n.withCredentials)||(m.withCredentials=!!n.withCredentials),d&&"json"!==d&&(m.responseType=n.responseType),h&&([c,u]=Oe(h,!0),m.addEventListener("progress",c)),f&&m.upload&&([a,l]=Oe(f),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(n.cancelToken||n.signal)&&(i=t=>{m&&(r(!t||t.type?new Ee(null,e,m):t),m.abort(),m=null)},n.cancelToken&&n.cancelToken.subscribe(i),n.signal&&(n.signal.aborted?i():n.signal.addEventListener("abort",i)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(n.url);y&&-1===le.protocols.indexOf(y)?r(new M("Unsupported protocol "+y+":",M.ERR_BAD_REQUEST,e)):m.send(o||null)}))},xe=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController;const o=function(e){if(!r){r=!0,i();const t=e instanceof Error?e:this.reason;n.abort(t instanceof M?t:new Ee(t instanceof Error?t.message:t))}};let s=t&&setTimeout((()=>{s=null,o(new M(`timeout ${t} of ms exceeded`,M.ETIMEDOUT))}),t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:a}=n;return a.unsubscribe=()=>z.asap(i),a}},Ue=function*(e,t){let r=e.byteLength;if(r<t)return void(yield e);let n,o=0;for(;o<r;)n=o+t,yield e.slice(o,n),o=n},ke=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},Fe=(e,t,r,n)=>{const o=async function*(e,t){for await(const r of ke(e))yield*Ue(r,t)}(e,t);let s,i=0,a=e=>{s||(s=!0,n&&n(e))};return new ReadableStream({async pull(e){try{const{done:t,value:n}=await o.next();if(t)return a(),void e.close();let s=n.byteLength;if(r){let e=i+=s;r(e)}e.enqueue(new Uint8Array(n))}catch(t){throw a(t),t}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},Le="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,De=Le&&"function"==typeof ReadableStream,Be=Le&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ie=(e,...t)=>{try{return!!e(...t)}catch(r){return!1}},qe=De&&Ie((()=>{let e=!1;const t=new Request(le.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),ze=De&&Ie((()=>z.isReadableStream(new Response("").body))),Me={stream:ze&&(e=>e.body)};var $e;Le&&($e=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Me[e]&&(Me[e]=z.isFunction($e[e])?t=>t[e]():(t,r)=>{throw new M(`Response type '${e}' is not supported`,M.ERR_NOT_SUPPORT,r)})})));const We=async(e,t)=>{const r=z.toFiniteNumber(e.getContentLength());return null==r?(async e=>{if(null==e)return 0;if(z.isBlob(e))return e.size;if(z.isSpecCompliantForm(e)){const t=new Request(le.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return z.isArrayBufferView(e)||z.isArrayBuffer(e)?e.byteLength:(z.isURLSearchParams(e)&&(e+=""),z.isString(e)?(await Be(e)).byteLength:void 0)})(t):r},He={http:null,xhr:Ne,fetch:Le&&(async e=>{let{url:t,method:r,data:n,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Pe(e);l=l?(l+"").toLowerCase():"text";let h,p=xe([o,s&&s.toAbortSignal()],i);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let g;try{if(c&&qe&&"get"!==r&&"head"!==r&&0!==(g=await We(u,n))){let e,r=new Request(t,{method:"POST",body:n,duplex:"half"});if(z.isFormData(n)&&(e=r.headers.get("content-type"))&&u.setContentType(e),r.body){const[e,t]=Se(g,Oe(Te(c)));n=Fe(r.body,65536,e,t)}}z.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;h=new Request(t,{...f,signal:p,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:o?d:void 0});let s=await fetch(h);const i=ze&&("stream"===l||"response"===l);if(ze&&(a||i&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=s[t]}));const t=z.toFiniteNumber(s.headers.get("content-length")),[r,n]=a&&Se(t,Oe(Te(a),!0))||[];s=new Response(Fe(s.body,65536,r,(()=>{n&&n(),m&&m()})),e)}l=l||"text";let y=await Me[z.findKey(Me,l)||"text"](s,e);return!i&&m&&m(),await new Promise(((t,r)=>{Re(t,r,{data:y,headers:ye.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:h})}))}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new M("Network Error",M.ERR_NETWORK,e,h),{cause:y.cause||y});throw M.from(y,y&&y.code,e,h)}})};z.forEach(He,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(r){}Object.defineProperty(e,"adapterName",{value:t})}}));const Je=e=>`- ${e}`,Ke=e=>z.isFunction(e)||null===e||!1===e,Ve=e=>{e=z.isArray(e)?e:[e];const{length:t}=e;let r,n;const o={};for(let s=0;s<t;s++){let t;if(r=e[s],n=r,!Ke(r)&&(n=He[(t=String(r)).toLowerCase()],void 0===n))throw new M(`Unknown adapter '${t}'`);if(n)break;o[t||"#"+s]=n}if(!n){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new M("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Je).join("\n"):" "+Je(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return n};function Xe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ee(null,e)}function Ge(e){Xe(e),e.headers=ye.from(e.headers),e.data=we.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Ve(e.adapter||de.adapter)(e).then((function(t){return Xe(e),t.data=we.call(e,e.transformResponse,t),t.headers=ye.from(t.headers),t}),(function(t){return be(t)||(Xe(e),t&&t.response&&(t.response.data=we.call(e,e.transformResponse,t.response),t.response.headers=ye.from(t.response.headers))),Promise.reject(t)}))}const Qe="1.9.0",Ye={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Ye[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));const Ze={};Ye.transitional=function(e,t,r){function n(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,o,s)=>{if(!1===e)throw new M(n(o," has been removed"+(t?" in "+t:"")),M.ERR_DEPRECATED);return t&&!Ze[o]&&(Ze[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,o,s)}},Ye.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};const et={assertOptions:function(e,t,r){if("object"!=typeof e)throw new M("options must be an object",M.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let o=n.length;for(;o-- >0;){const s=n[o],i=t[s];if(i){const t=e[s],r=void 0===t||i(t,s,e);if(!0!==r)throw new M("option "+s+" must be "+r,M.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new M("Unknown option "+s,M.ERR_BAD_OPTION)}},validators:Ye},tt=et.validators;let rt=class{constructor(e){this.defaults=e||{},this.interceptors={request:new te,response:new te}}async request(e,t){try{return await this._request(e,t)}catch(r){if(r instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{r.stack?t&&!String(r.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(r.stack+="\n"+t):r.stack=t}catch(n){}}throw r}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=_e(this.defaults,t);const{transitional:r,paramsSerializer:n,headers:o}=t;void 0!==r&&et.assertOptions(r,{silentJSONParsing:tt.transitional(tt.boolean),forcedJSONParsing:tt.transitional(tt.boolean),clarifyTimeoutError:tt.transitional(tt.boolean)},!1),null!=n&&(z.isFunction(n)?t.paramsSerializer={serialize:n}:et.assertOptions(n,{encode:tt.function,serialize:tt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),et.assertOptions(t,{baseUrl:tt.spelling("baseURL"),withXsrfToken:tt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&z.merge(o.common,o[t.method]);o&&z.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=ye.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,d=0;if(!a){const e=[Ge.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=i.length;let f=t;for(d=0;d<u;){const e=i[d++],t=i[d++];try{f=e(f)}catch(h){t.call(this,h);break}}try{l=Ge.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return ee(Ce((e=_e(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};z.forEach(["delete","get","head","options"],(function(e){rt.prototype[e]=function(t,r){return this.request(_e(r||{},{method:e,url:t,data:(r||{}).data}))}})),z.forEach(["post","put","patch"],(function(e){function t(t){return function(r,n,o){return this.request(_e(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}rt.prototype[e]=t(),rt.prototype[e+"Form"]=t(!0)}));const nt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(nt).forEach((([e,t])=>{nt[t]=e}));const ot=function t(r){const n=new rt(r),o=e(rt.prototype.request,n);return z.extend(o,rt.prototype,n,{allOwnKeys:!0}),z.extend(o,n,null,{allOwnKeys:!0}),o.create=function(e){return t(_e(r,e))},o}(de);ot.Axios=rt,ot.CanceledError=Ee,ot.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const r=this;this.promise.then((e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null})),this.promise.then=e=>{let t;const n=new Promise((e=>{r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e((function(e,n,o){r.reason||(r.reason=new Ee(e,n,o),t(r.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},ot.isCancel=be,ot.VERSION=Qe,ot.toFormData=X,ot.AxiosError=M,ot.Cancel=ot.CanceledError,ot.all=function(e){return Promise.all(e)},ot.spread=function(e){return function(t){return e.apply(null,t)}},ot.isAxiosError=function(e){return z.isObject(e)&&!0===e.isAxiosError},ot.mergeConfig=_e,ot.AxiosHeaders=ye,ot.formToJSON=e=>ue(z.isHTMLForm(e)?new FormData(e):e),ot.getAdapter=Ve,ot.HttpStatusCode=nt,ot.default=ot;const{Axios:st,AxiosError:it,CanceledError:at,isCancel:ct,CancelToken:lt,VERSION:ut,all:dt,Cancel:ft,isAxiosError:ht,spread:pt,toFormData:mt,AxiosHeaders:gt,HttpStatusCode:yt,formToJSON:wt,getAdapter:bt,mergeConfig:Et}=ot,Rt=()=>"undefined"!=typeof chrome&&void 0!==chrome.runtime&&"function"==typeof chrome.runtime.sendMessage,Ot=()=>"undefined"!=typeof chrome&&void 0!==chrome.runtime&&"function"==typeof chrome.runtime.getURL,St=()=>(Ot(),"https://www.kxianjunshi.com/api"),Tt=ot.create({baseURL:St(),timeout:3e4,headers:{"Content-Type":"application/json"}});let vt=[],At=0;const Ct=()=>{const e=localStorage.getItem("token");if(!e)return console.error("Token不存在"),!1;if(!e.startsWith("Token "))return console.error("Token格式错误"),!1;return 40===e.replace("Token ","").length||(console.error("Token长度错误"),!1)},jt=e=>!!e&&(e.includes("/auth/login")||e.includes("/auth/register")||e.includes("/auth/send-code")||e.includes("/auth/request-password-reset")||e.includes("/auth/reset-password-with-code")),_t=async()=>{const e=Date.now(),t=e-6e4;if(vt=vt.filter((e=>e.timestamp>t)),vt.length>=30){const t=6e4-(e-vt[0].timestamp);if(t>0)return console.log(`达到每分钟请求限制，等待${t/1e3}秒`),await new Promise((e=>setTimeout(e,t))),void(await _t())}const r=e-At;if(r<2e3){const e=2e3-r;console.log(`请求过于频繁，等待${e/1e3}秒`),await new Promise((t=>setTimeout(t,e)))}vt.push({timestamp:e,count:1}),At=e},Pt=async(e,t=0)=>{var r,n,o,s,i;try{if(!Ct())throw new Error("Token验证失败");await _t();const t=localStorage.getItem("token");let n;return t&&(e.headers.Authorization=t),e.headers["Cache-Control"]="no-cache",e.headers.Pragma="no-cache",(null==(r=e.params)?void 0:r.force_refresh)&&(e.timeout=6e4),Rt()?(console.log("使用代理发送请求:",e.url),n=await(async e=>{if(!Rt())throw new Error("不在扩展环境中，无法使用代理");return new Promise(((t,r)=>{const{url:n,method:o,headers:s,data:i}=e,a=null==n?void 0:n.includes("force_refresh=true");let c=null;const l=a?12e4:3e4;c=window.setTimeout((()=>{r(new Error(`请求超时 (${l/1e3}秒)`))}),l),chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:n,method:(null==o?void 0:o.toUpperCase())||"GET",headers:s,body:i}},(n=>{if(null!==c&&clearTimeout(c),chrome.runtime.lastError)return void r(new Error(chrome.runtime.lastError.message));if(!n||!n.success){const e=new Error((null==n?void 0:n.error)||"请求失败");return(null==n?void 0:n.errorDetail)&&(e.detail=n.errorDetail),void r(e)}const o={data:n.data,status:n.status,statusText:n.statusText,headers:n.headers,config:e};t(o)}))}))})(e)):n=await ot(e),n}catch(a){if("ERR_FILE_NOT_FOUND"===a.code&&(console.error("文件访问错误:",a),Ot()&&(null==(o=null==(n=a.config)?void 0:n.url)?void 0:o.includes(chrome.runtime.getURL("")))))return console.log("尝试重新加载扩展资源..."),chrome.runtime.sendMessage({type:"RELOAD_RESOURCES"}),await new Promise((e=>setTimeout(e,1e3))),Pt(e,t+1);if("Token验证失败"===a.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(a);const r=(null==(s=e.params)?void 0:s.force_refresh)?6:3;if(t<r&&("ERR_NETWORK"===a.code||"ERR_FILE_NOT_FOUND"===a.code||"ECONNABORTED"===a.code||500===(null==(i=a.response)?void 0:i.status))){const n=2e3*Math.pow(2,t);return console.log(`请求失败，${n/1e3}秒后重试(${t+1}/${r})`),await new Promise((e=>setTimeout(e,n))),Pt(e,t+1)}throw a}};Tt.interceptors.request.use((async e=>{try{if(!jt(e.url)&&!Ct())return Promise.reject(new Error("Token验证失败"));if(await _t(),!jt(e.url)){const t=localStorage.getItem("token");t&&(e.headers.Authorization=t)}return console.log("Request Config:",{url:e.url,method:e.method,data:e.data,headers:{...e.headers,Authorization:e.headers.Authorization?"Token ****":void 0}}),e}catch(t){return Promise.reject(t)}}),(e=>(console.error("Request Error:",e),Promise.reject(e)))),Tt.interceptors.response.use((e=>(console.log("API响应数据:",{url:e.config.url,status:e.status,data:e.data}),e.data&&"object"==typeof e.data?e.data:(console.error("API响应数据格式错误:",e.data),Promise.reject(new Error("数据格式错误"))))),(async e=>{var t,r,n,o,s,i,a,c,l,u;const d=e.config;if("Token验证失败"===e.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(e);if("ERR_NETWORK"===e.code&&(console.error("网络连接错误:",e),!d._retry)){d._retry=!0;try{return await Pt(d)}catch(f){return console.error("重试失败:",f),Promise.reject(f)}}if(("ERR_FILE_NOT_FOUND"===e.code||500===(null==(t=e.response)?void 0:t.status))&&!d._retry){d._retry=!0;try{return await Pt(d)}catch(f){return console.error("重试失败:",f),Promise.reject(f)}}return 401===(null==(r=e.response)?void 0:r.status)&&(localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login"),console.error("Response Error:",{status:null==(n=e.response)?void 0:n.status,data:null==(o=e.response)?void 0:o.data,headers:null==(s=e.response)?void 0:s.headers,code:e.code,message:e.message,config:{url:null==(i=e.config)?void 0:i.url,method:null==(a=e.config)?void 0:a.method,headers:{...null==(c=e.config)?void 0:c.headers,Authorization:(null==(u=null==(l=e.config)?void 0:l.headers)?void 0:u.Authorization)?"Token ****":void 0}}}),Promise.reject(e)}));const Nt={sendCode:e=>{const t=`${St()}/auth/send-code/`;return ot.post(t,{email:e.email.trim()},{headers:{"Content-Type":"application/json"}}).then((e=>e.data))},register:e=>{const t=`${St()}/auth/register/`;return ot.post(t,{email:e.email.trim(),password:e.password.trim(),code:e.code.trim(),invitation_code:e.invitation_code.trim()},{headers:{"Content-Type":"application/json"}}).then((e=>e.data))},login:e=>{const t=`${St()}/auth/login/`;return ot.post(t,{email:e.email.trim(),password:e.password.trim()},{headers:{"Content-Type":"application/json"}}).then((e=>e.data))},requestPasswordReset:e=>{const t=`${St()}/auth/request-password-reset/`;return ot.post(t,{email:e.email.trim()},{headers:{"Content-Type":"application/json"}}).then((e=>e.data))},resetPasswordWithCode:e=>{const t=`${St()}/auth/reset-password-with-code/`;return ot.post(t,{email:e.email.trim(),code:e.code.trim(),new_password:e.new_password.trim(),confirm_password:e.confirm_password.trim()},{headers:{"Content-Type":"application/json"}}).then((e=>e.data))},changePassword:e=>{const t=`${St()}/auth/change-password/`;return ot.post(t,{current_password:e.current_password.trim(),new_password:e.new_password.trim(),confirm_password:e.confirm_password.trim()},{headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")||""}}).then((e=>e.data))}},xt=async(e,t=!1)=>{try{const r=e.toUpperCase(),n=`/crypto/technical-indicators/${r.endsWith("USDT")?r:`${r}USDT`}/`,o={};t&&(o.force_refresh=!0);const s=`${St()}${n}`,i=(await ot.get(s,{params:o,headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")||""}})).data;if("object"==typeof i&&"status"in i){if("not_found"===i.status)return i;if("success"===i.status&&"data"in i)return i.data}return i}catch(r){if("ERR_NETWORK"===r.code)throw new Error("网络连接错误，请检查您的网络连接");throw r}};export{Nt as a,Tt as b,ot as c,xt as g};
