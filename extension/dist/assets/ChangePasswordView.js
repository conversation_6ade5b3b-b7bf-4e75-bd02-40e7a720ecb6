import{q as e,r as s,c as a,t as r,z as o,u as l,a3 as t,H as n,K as d,a1 as u,Y as i,J as p,U as c,D as m,a4 as w,V as v,v as f}from"./main.js";import{a as g}from"./index.js";const b={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},y={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},x={class:"max-w-[375px] mx-auto"},_={class:"flex items-center px-4 py-3"},k={class:"flex-1 pt-16 pb-16"},h={class:"max-w-[375px] mx-auto px-4"},S={key:0,class:"text-center py-8"},V={key:1},q={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},A={key:1,class:"p-3 rounded-lg bg-green-500/10 border border-green-500/20 text-green-400 text-sm"},N=["disabled"],O=e({__name:"ChangePasswordView",setup(e){const O=t(),U=s(!1),j=s(null),z=s(null),C=s({current_password:"",new_password:"",confirm_password:""}),J=a((()=>!!localStorage.getItem("token"))),T=async()=>{var e;if(j.value=null,z.value=null,!C.value.current_password||!C.value.new_password||!C.value.confirm_password)return void(j.value="请填写所有必填字段");if(C.value.new_password!==C.value.confirm_password)return void(j.value="两次输入的新密码不一致");if(C.value.new_password.length<6)return void(j.value="密码长度至少为6位");const s=/[A-Za-z]/.test(C.value.new_password),a=/[0-9]/.test(C.value.new_password);if(s&&a){U.value=!0;try{const s=await g.changePassword({current_password:C.value.current_password,new_password:C.value.new_password,confirm_password:C.value.confirm_password});"success"===s.status?(z.value="密码修改成功",(null==(e=s.data)?void 0:e.token)&&localStorage.setItem("token",`Token ${s.data.token}`),C.value={current_password:"",new_password:"",confirm_password:""},setTimeout((()=>{O.push("/profile")}),3e3)):j.value=s.message||"修改密码失败，请稍后重试"}catch(r){console.error("修改密码失败:",r),r.response?r.response.data&&"object"==typeof r.response.data?r.response.data.message?j.value="string"==typeof r.response.data.message?r.response.data.message:JSON.stringify(r.response.data.message):r.response.data.detail?j.value="string"==typeof r.response.data.detail?r.response.data.detail:JSON.stringify(r.response.data.detail):j.value="修改密码失败，请稍后重试":j.value="修改密码失败，请稍后重试":"ECONNABORTED"===r.code?j.value="网络连接超时，请检查网络":j.value="修改密码失败，请稍后重试"}finally{U.value=!1}}else j.value="密码必须包含字母和数字"};return(e,s)=>{const a=u("router-link");return f(),r("div",b,[o("header",y,[o("div",x,[o("div",_,[o("button",{onClick:s[0]||(s[0]=e=>l(O).push("/profile")),class:"mr-2"},s[4]||(s[4]=[o("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),s[5]||(s[5]=o("h1",{class:"text-lg font-semibold"},"修改密码",-1))])])]),o("main",k,[o("div",h,[J.value?(f(),r("div",V,[s[12]||(s[12]=o("p",{class:"text-gray-400 mb-6"},"请输入当前密码和新密码",-1)),o("form",{onSubmit:i(T,["prevent"]),class:"space-y-6"},[j.value?(f(),r("div",q,c(j.value),1)):p("",!0),z.value?(f(),r("div",A,c(z.value),1)):p("",!0),o("div",null,[s[8]||(s[8]=o("label",{for:"current_password",class:"block text-sm font-medium text-gray-300 mb-1"},"当前密码",-1)),m(o("input",{id:"current_password","onUpdate:modelValue":s[1]||(s[1]=e=>C.value.current_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none",required:""},null,512),[[w,C.value.current_password]])]),o("div",null,[s[9]||(s[9]=o("label",{for:"new_password",class:"block text-sm font-medium text-gray-300 mb-1"},"新密码",-1)),m(o("input",{id:"new_password","onUpdate:modelValue":s[2]||(s[2]=e=>C.value.new_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none",required:""},null,512),[[w,C.value.new_password]]),s[10]||(s[10]=o("p",{class:"mt-1 text-xs text-gray-500"},"密码至少6位，包含字母和数字",-1))]),o("div",null,[s[11]||(s[11]=o("label",{for:"confirm_password",class:"block text-sm font-medium text-gray-300 mb-1"},"确认新密码",-1)),m(o("input",{id:"confirm_password","onUpdate:modelValue":s[3]||(s[3]=e=>C.value.confirm_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none",required:""},null,512),[[w,C.value.confirm_password]])]),o("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium",disabled:U.value},c(U.value?"提交中...":"修改密码"),9,N)],32)])):(f(),r("div",S,[s[7]||(s[7]=o("p",{class:"text-gray-400 mb-4"},"请先登录后再修改密码",-1)),n(a,{to:"/login?redirect=/change-password",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:d((()=>s[6]||(s[6]=[v(" 去登录 ")]))),_:1})]))])])])}}});export{O as default};
