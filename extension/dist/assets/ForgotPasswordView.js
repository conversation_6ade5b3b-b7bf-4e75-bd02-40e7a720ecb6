import{q as e,r as a,e as s,t as r,z as l,u as o,a3 as t,J as d,U as n,Y as u,D as i,a4 as c,C as p,$ as m,H as v,K as f,a1 as g,V as w,v as y}from"./main.js";import{a as b}from"./index.js";const x={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},_={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},h={class:"max-w-[375px] mx-auto"},k={class:"flex items-center px-4 py-3"},I={class:"flex-1 pt-16 pb-16"},N={class:"max-w-[375px] mx-auto px-4"},O={key:0,class:"mb-4 p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-400 text-sm"},S={key:0,class:"mt-1 text-sm text-red-500"},J={class:"flex gap-2"},j={class:"flex-1"},A={key:0,class:"mt-1 text-sm text-red-500"},C=["disabled"],P={key:0,class:"mt-1 text-sm text-red-500"},q={key:0,class:"mt-1 text-sm text-red-500"},D=["disabled"],F={key:1,class:"fixed inset-0 flex items-center justify-center z-50 bg-black/70"},V={class:"mt-6 text-center"},U=e({__name:"ForgotPasswordView",setup(e){const U=t(),z=a(!1),E=a(!1),R=a(0),T=a(!1),B=a({email:"",code:"",new_password:"",confirm_password:""}),$=a(""),H=a(null),K=a(null),W=a(null),Y=a(null),Z=a({email:"",code:"",new_password:"",confirm_password:""}),G=()=>{B.value={email:"",code:"",new_password:"",confirm_password:""},$.value=""},L=e=>{const a={email:H,code:K,new_password:W,confirm_password:Y}[e];a.value&&setTimeout((()=>{var e;null==(e=a.value)||e.focus()}),100)},M=()=>{localStorage.setItem("resetPasswordFormData",JSON.stringify({email:Z.value.email,code:Z.value.code}))},Q=()=>{B.value.email="",$.value="",M()},X=()=>{B.value.code="",$.value="",M()},ee=()=>{B.value.new_password="",$.value=""},ae=()=>{B.value.confirm_password="",$.value=""},se=async()=>{var e;if(G(),!Z.value.email)return B.value.email="请输入邮箱",void L("email");E.value=!0;try{console.log("发送验证码请求:",Z.value.email);const e=await b.requestPasswordReset({email:Z.value.email.trim()});console.log("验证码请求响应:",e),"success"===e.status?((()=>{R.value=60;const e=setInterval((()=>{R.value--,R.value<=0&&clearInterval(e)}),1e3)})(),$.value=""):$.value=e.message||"发送验证码失败，请稍后重试"}catch(a){console.error("发送验证码失败:",a),a.response&&(console.error("错误响应详情:",{status:a.response.status,data:a.response.data,headers:a.response.headers}),console.log("服务器错误详情:",JSON.stringify(a.response.data,null,2)),a.response.data&&"object"==typeof a.response.data?a.response.data.message?$.value="string"==typeof a.response.data.message?a.response.data.message:JSON.stringify(a.response.data.message):a.response.data.detail?$.value="string"==typeof a.response.data.detail?a.response.data.detail:JSON.stringify(a.response.data.detail):$.value="服务器邮件发送功能超时，请联系管理员或稍后再试":$.value="服务器邮件发送功能超时，请联系管理员或稍后再试"),404===(null==(e=a.response)?void 0:e.status)?(B.value.email="该邮箱未注册",L("email")):"ECONNABORTED"===a.code&&($.value="网络连接超时，请检查网络")}finally{E.value=!1}},re=async()=>{var e,a,s;G();let r=!1;if(Z.value.email||(B.value.email="请输入邮箱",r||(L("email"),r=!0)),Z.value.code||(B.value.code="请输入验证码",r||(L("code"),r=!0)),Z.value.new_password){const e=/[A-Za-z]/.test(Z.value.new_password),a=/[0-9]/.test(Z.value.new_password);Z.value.new_password.length<6?(B.value.new_password="密码长度至少为6位",r||(L("new_password"),r=!0)):e&&a||(B.value.new_password="密码必须包含字母和数字",r||(L("new_password"),r=!0))}else B.value.new_password="请输入新密码",r||(L("new_password"),r=!0);if(Z.value.confirm_password?Z.value.new_password!==Z.value.confirm_password&&(B.value.confirm_password="两次输入的密码不一致",r||(L("confirm_password"),r=!0)):(B.value.confirm_password="请确认密码",r||(L("confirm_password"),r=!0)),!r){z.value=!0;try{console.log("重置密码请求:",{email:Z.value.email,code:Z.value.code,new_password:"******",confirm_password:"******"});const e=await b.resetPasswordWithCode({email:Z.value.email.trim(),code:Z.value.code.trim(),new_password:Z.value.new_password.trim(),confirm_password:Z.value.confirm_password.trim()});console.log("重置密码响应:",e),"success"===e.status?(localStorage.removeItem("resetPasswordFormData"),T.value=!0):$.value=e.message||"重置密码失败，请稍后重试"}catch(l){console.error("重置密码失败:",l),l.response&&(console.error("错误响应详情:",{status:l.response.status,data:l.response.data,headers:l.response.headers}),console.log("服务器错误详情:",JSON.stringify(l.response.data,null,2)),l.response.data&&"object"==typeof l.response.data?l.response.data.message?$.value="string"==typeof l.response.data.message?l.response.data.message:JSON.stringify(l.response.data.message):l.response.data.detail?$.value="string"==typeof l.response.data.detail?l.response.data.detail:JSON.stringify(l.response.data.detail):$.value="服务器邮件发送功能超时，请联系管理员或稍后再试":$.value="服务器邮件发送功能超时，请联系管理员或稍后再试"),400===(null==(e=l.response)?void 0:e.status)?(null==(s=null==(a=l.response.data)?void 0:a.message)?void 0:s.code)?(B.value.code=Array.isArray(l.response.data.message.code)?l.response.data.message.code[0]:l.response.data.message.code,L("code")):(B.value.code="验证码无效或已过期",L("code")):"ECONNABORTED"===l.code&&($.value="网络连接超时，请检查网络")}finally{z.value=!1}}},le=()=>{T.value=!1,U.push("/login")};return s((()=>{(()=>{const e=localStorage.getItem("resetPasswordFormData");if(e)try{const{email:a,code:s}=JSON.parse(e);Z.value.email=a||"",Z.value.code=s||""}catch(a){console.error("恢复表单数据失败:",a)}})()})),(e,a)=>{const s=g("router-link");return y(),r("div",x,[l("header",_,[l("div",h,[l("div",k,[l("button",{onClick:a[0]||(a[0]=e=>o(U).push("/login")),class:"mr-2"},a[5]||(a[5]=[l("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),a[6]||(a[6]=l("h1",{class:"text-lg font-semibold"},"忘记密码",-1))])])]),l("main",I,[l("div",N,[$.value?(y(),r("div",O,n($.value),1)):d("",!0),l("form",{onSubmit:u(re,["prevent"]),class:"space-y-4"},[l("div",null,[a[7]||(a[7]=l("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"邮箱",-1)),i(l("input",{type:"email","onUpdate:modelValue":a[1]||(a[1]=e=>Z.value.email=e),onInput:Q,required:"",ref_key:"emailInput",ref:H,class:p(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",B.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入邮箱"},null,34),[[c,Z.value.email]]),B.value.email?(y(),r("p",S,n(B.value.email),1)):d("",!0)]),l("div",J,[l("div",j,[a[8]||(a[8]=l("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"验证码",-1)),i(l("input",{type:"text","onUpdate:modelValue":a[2]||(a[2]=e=>Z.value.code=e),onInput:X,required:"",ref_key:"codeInput",ref:K,class:p(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",B.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入验证码"},null,34),[[c,Z.value.code]]),B.value.code?(y(),r("p",A,n(B.value.code),1)):d("",!0)]),l("button",{type:"button",onClick:se,disabled:E.value||R.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed"},n(R.value>0?`${R.value}s后重试`:E.value?"发送中...":"获取验证码"),9,C)]),l("div",null,[a[9]||(a[9]=l("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"新密码",-1)),i(l("input",{type:"password","onUpdate:modelValue":a[3]||(a[3]=e=>Z.value.new_password=e),onInput:ee,required:"",ref_key:"passwordInput",ref:W,class:p(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",B.value.new_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请输入新密码"},null,34),[[c,Z.value.new_password]]),B.value.new_password?(y(),r("p",P,n(B.value.new_password),1)):d("",!0),a[10]||(a[10]=l("p",{class:"mt-1 text-xs text-gray-500"},"密码至少6位，包含字母和数字",-1))]),l("div",null,[a[11]||(a[11]=l("label",{class:"block text-sm font-medium text-gray-400 mb-1"},"确认密码",-1)),i(l("input",{type:"password","onUpdate:modelValue":a[4]||(a[4]=e=>Z.value.confirm_password=e),onInput:ae,required:"",ref_key:"confirmPasswordInput",ref:Y,class:p(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none",B.value.confirm_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:"请再次输入新密码"},null,34),[[c,Z.value.confirm_password]]),B.value.confirm_password?(y(),r("p",q,n(B.value.confirm_password),1)):d("",!0)]),l("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium",disabled:z.value},n(z.value?"重置中...":"重置密码"),9,D)],32),T.value?(y(),r("div",F,[l("div",{class:"bg-gray-900 rounded-lg p-6 max-w-xs w-full mx-4"},[a[12]||(a[12]=m('<div class="mb-4 flex justify-center"><div class="w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center"><i class="ri-check-line ri-2x text-green-500"></i></div></div><h2 class="text-xl font-semibold mb-2 text-center">密码重置成功</h2><p class="text-gray-400 mb-6 text-center">您的密码已成功重置，请使用新密码登录。</p>',3)),l("button",{onClick:le,class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"}," 返回登录 ")])])):d("",!0),l("div",V,[v(s,{to:"/login",class:"text-primary hover:underline"},{default:f((()=>a[13]||(a[13]=[w(" 返回登录 ")]))),_:1})])])])])}}});export{U as default};
