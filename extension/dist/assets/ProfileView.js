import{q as e,r as t,c as l,e as a,t as s,z as r,H as i,K as o,a1 as n,F as c,U as d,$ as u,V as m,a3 as f,v as x}from"./main.js";import{b as g,c as p}from"./index.js";const b={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-scroll"},y={class:"flex-1 pt-16 pb-16"},h={class:"max-w-[375px] mx-auto px-4"},v={key:0,class:"bg-gray-800 rounded-lg p-6 mb-6"},w={class:"text-center"},k={class:"bg-gray-800 rounded-lg p-6 mb-6"},j={class:"flex items-center space-x-4"},_={class:"w-16 h-16 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-2xl font-bold overflow-hidden"},I={class:"flex-1"},S={class:"text-base font-semibold"},z={class:"text-gray-500 text-xs mt-1"},A={class:"space-y-4"},C={class:"fixed bottom-0 w-full bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},F={class:"max-w-[375px] mx-auto"},U={class:"grid grid-cols-2 h-16"},N=e({__name:"ProfileView",setup(e){const N=f(),D=t({id:0,email:"",created_at:"",updated_at:""}),J=l((()=>!!localStorage.getItem("token"))),L=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},O=()=>{localStorage.removeItem("token"),localStorage.removeItem("userInfo"),N.push("/login")};return a((()=>{(async()=>{if(J.value)try{const e=localStorage.getItem("userInfo");e&&(D.value=JSON.parse(e));const t=`${g.defaults.baseURL}/auth/profile/`,l=(await p.get(t,{headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")||""}})).data;"success"===(null==l?void 0:l.status)&&(null==l?void 0:l.data)&&(D.value=l.data,localStorage.setItem("userInfo",JSON.stringify(l.data)))}catch(e){}})()})),(e,t)=>{var l,a;const f=n("router-link");return x(),s("div",b,[t[9]||(t[9]=r("header",{class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},[r("div",{class:"max-w-[375px] mx-auto"},[r("div",{class:"flex items-center h-12 px-4"},[r("h1",{class:"text-lg font-semibold"},"个人中心")])])],-1)),r("main",y,[r("div",h,[J.value?(x(),s(c,{key:1},[r("div",k,[r("div",j,[r("div",_,d((null==(a=null==(l=D.value.email)?void 0:l[0])?void 0:a.toUpperCase())||"U"),1),r("div",I,[r("h2",S,d(D.value.email),1),r("p",z,"注册时间: "+d(L(D.value.created_at)),1)])])]),r("div",A,[i(f,{to:"/change-password",class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},{default:o((()=>t[4]||(t[4]=[r("i",{class:"ri-lock-password-line mr-3"},null,-1),m(" 修改密码 "),r("i",{class:"ri-arrow-right-s-line ml-auto"},null,-1)]))),_:1}),t[6]||(t[6]=u('<button class="w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"><i class="ri-settings-3-line mr-3"></i> 设置 <i class="ri-arrow-right-s-line ml-auto"></i></button><a href="https://www.kxianjunshi.com/privacy-policy/" target="_blank" class="w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"><i class="ri-shield-check-line mr-3"></i> 隐私政策 <i class="ri-external-link-line ml-auto"></i></a><button class="w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"><i class="ri-information-line mr-3"></i> 关于我们 <i class="ri-arrow-right-s-line ml-auto"></i></button>',3)),r("button",{class:"w-full py-3 px-4 bg-red-500 text-white rounded-lg font-medium flex items-center",onClick:O},t[5]||(t[5]=[r("i",{class:"ri-logout-box-line mr-3"},null,-1),m(" 退出登录 ")]))])],64)):(x(),s("div",v,[r("div",w,[t[1]||(t[1]=r("div",{class:"w-20 h-20 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-3xl font-bold mx-auto mb-4"},[r("i",{class:"ri-user-3-line"})],-1)),t[2]||(t[2]=r("h2",{class:"text-lg font-semibold mb-2"},"未登录",-1)),t[3]||(t[3]=r("p",{class:"text-gray-400 text-sm mb-4"},"登录后查看个人中心",-1)),i(f,{to:"/login",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:o((()=>t[0]||(t[0]=[m(" 立即登录 ")]))),_:1})])]))])]),r("nav",C,[r("div",F,[r("div",U,[i(f,{to:"/",class:"flex flex-col items-center justify-center text-gray-500"},{default:o((()=>t[7]||(t[7]=[r("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1),r("span",{class:"text-xs mt-0.5"},"行情",-1)]))),_:1}),i(f,{to:"/profile",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:o((()=>t[8]||(t[8]=[r("i",{class:"ri-user-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1),r("span",{class:"text-xs mt-0.5"},"我的",-1)]))),_:1})])])])])}}});export{N as default};
