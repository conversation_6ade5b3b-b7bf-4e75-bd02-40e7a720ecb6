import{q as e,r as s,t as a,z as l,u as r,a3 as o,Y as t,J as n,U as u,D as d,a4 as i,H as c,K as m,a1 as p,V as v,a5 as g,v as f}from"./main.js";import{a as b}from"./index.js";const x={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},y={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},w={class:"max-w-[375px] mx-auto"},k={class:"flex items-center px-4 py-3"},h={class:"flex-1 pt-16 pb-16"},S={class:"max-w-[375px] mx-auto px-4"},I={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},j={class:"flex justify-between items-center mb-1"},O=["disabled"],q={class:"mt-6 text-center"},N={class:"text-sm text-gray-400"},V=e({__name:"LoginView",setup(e){const V=o(),_=g(),A=s({email:"",password:""}),J=s(!1),T=s(null),U=async()=>{var e,s,a,l,r,o;if(T.value=null,A.value.email&&A.value.password){J.value=!0;try{const e=await b.login({email:A.value.email.trim(),password:A.value.password.trim()});if(console.log("登录响应:",e),"success"===e.status&&e.data){const s=e.data.token,a=e.data.user;if(s){localStorage.setItem("token",`Token ${s}`),a&&localStorage.setItem("userInfo",JSON.stringify(a)),console.log("已保存token:",localStorage.getItem("token")),console.log("已保存用户信息:",localStorage.getItem("userInfo"));const e=_.query.redirect||"/";V.push(e)}else console.error("Token未找到",e),T.value="登录失败：未获取到 token"}else console.error("登录响应格式错误",e),T.value=e.message||"登录失败：服务器响应格式错误"}catch(t){if(console.error("登录失败:",t),t.response&&(console.error("错误响应详情:",{status:t.response.status,data:t.response.data,headers:t.response.headers}),console.log("服务器错误详情:",JSON.stringify(t.response.data,null,2))),null==(s=null==(e=t.response)?void 0:e.data)?void 0:s.message)if("object"==typeof t.response.data.message){const e=Object.values(t.response.data.message).flat();e.length>0?T.value=e[0]:T.value="登录失败，请检查输入"}else T.value=t.response.data.message;else(null==(l=null==(a=t.response)?void 0:a.data)?void 0:l.detail)?T.value=t.response.data.detail:401===(null==(r=t.response)?void 0:r.status)?T.value="邮箱或密码错误":429===(null==(o=t.response)?void 0:o.status)?T.value="登录尝试次数过多，请稍后再试":"ECONNABORTED"===t.code?T.value="网络连接超时，请检查网络":T.value="登录失败，请稍后重试"}finally{J.value=!1}}else T.value="请填写所有必填字段"};return(e,s)=>{const o=p("router-link");return f(),a("div",x,[l("header",y,[l("div",w,[l("div",k,[l("button",{onClick:s[0]||(s[0]=e=>r(V).push("/")),class:"mr-2"},s[3]||(s[3]=[l("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),s[4]||(s[4]=l("h1",{class:"text-lg font-semibold"},"登录",-1))])])]),l("main",h,[l("div",S,[l("form",{onSubmit:t(U,["prevent"]),class:"space-y-6"},[T.value?(f(),a("div",I,u(T.value),1)):n("",!0),l("div",null,[s[5]||(s[5]=l("label",{for:"email",class:"block text-sm font-medium text-gray-300 mb-1"},"邮箱",-1)),d(l("input",{id:"email","onUpdate:modelValue":s[1]||(s[1]=e=>A.value.email=e),type:"email",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none",required:""},null,512),[[i,A.value.email]])]),l("div",null,[l("div",j,[s[7]||(s[7]=l("label",{for:"password",class:"block text-sm font-medium text-gray-300"},"密码",-1)),c(o,{to:"/forgot-password",class:"text-xs text-primary hover:underline"},{default:m((()=>s[6]||(s[6]=[v("忘记密码？")]))),_:1})]),d(l("input",{id:"password","onUpdate:modelValue":s[2]||(s[2]=e=>A.value.password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none",required:""},null,512),[[i,A.value.password]])]),l("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium",disabled:J.value},u(J.value?"登录中...":"登录"),9,O)],32),l("div",q,[l("p",N,[s[9]||(s[9]=v(" 还没有账号？ ")),c(o,{to:"/register",class:"text-primary hover:underline"},{default:m((()=>s[8]||(s[8]=[v("立即注册")]))),_:1})])])])])])}}});export{V as default};
