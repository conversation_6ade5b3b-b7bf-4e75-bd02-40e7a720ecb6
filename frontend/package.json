{"name": "frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:css": "tailwindcss -i ./src/styles/main.css -o ./src/styles/output.css --watch"}, "dependencies": {"@vitejs/plugin-vue-jsx": "^4.1.2", "@vueuse/core": "^13.1.0", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.9", "html2canvas": "^1.4.1", "pinia": "^2.1.7", "qrcode": "^1.5.4", "qrcode.js": "^0.0.1", "remixicon": "^4.2.0", "tippy.js": "^6.3.7", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@types/chrome": "^0.0.317", "@vitejs/plugin-vue": "^5.0.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.12", "vue-tsc": "^1.8.27"}}