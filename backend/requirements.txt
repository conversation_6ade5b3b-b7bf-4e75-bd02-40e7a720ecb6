# Web框架
Django==5.0.2
djangorestframework==3.14.0
django-cors-headers==4.3.1

# 环境配置
python-dotenv==1.0.1
gunicorn==21.2.0

# 数据分析
numpy==1.26.4
pandas==2.2.1
ta-lib==0.4.28

# 网络请求
requests==2.28.1
python-binance==1.0.19
aiohttp==3.9.3

# 数据库
PyMySQL==1.1.0
django-celery-results==2.5.1

# 异步任务
celery==5.3.6
redis==5.0.1
django-celery-beat==2.5.0

# 安全
django-filter==23.5
whitenoise==6.6.0

# 部署
mysqlclient==2.2.1
uwsgi==2.0.23