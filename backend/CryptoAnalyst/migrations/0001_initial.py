# Generated by Django 5.0.2 on 2025-04-29 02:00

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Chain",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("chain", models.CharField(max_length=50, unique=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_testnet", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "区块链",
                "verbose_name_plural": "区块链",
            },
        ),
        migrations.CreateModel(
            name="InvitationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code", models.CharField(max_length=20, unique=True)),
                ("is_used", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="VerificationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254, verbose_name="邮箱")),
                ("code", models.CharField(max_length=6, verbose_name="验证码")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="过期时间")),
                (
                    "is_used",
                    models.BooleanField(default=False, verbose_name="是否已使用"),
                ),
            ],
            options={
                "verbose_name": "验证码",
                "verbose_name_plural": "验证码",
            },
        ),
        migrations.CreateModel(
            name="Token",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(max_length=20, unique=True)),
                ("name", models.CharField(max_length=100)),
                ("address", models.CharField(blank=True, max_length=100)),
                ("decimals", models.IntegerField(default=18)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "chain",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tokens",
                        to="CryptoAnalyst.chain",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TechnicalAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("rsi", models.FloatField(null=True)),
                ("macd_line", models.FloatField(null=True)),
                ("macd_signal", models.FloatField(null=True)),
                ("macd_histogram", models.FloatField(null=True)),
                ("bollinger_upper", models.FloatField(null=True)),
                ("bollinger_middle", models.FloatField(null=True)),
                ("bollinger_lower", models.FloatField(null=True)),
                ("bias", models.FloatField(null=True)),
                ("psy", models.FloatField(null=True)),
                ("dmi_plus", models.FloatField(null=True)),
                ("dmi_minus", models.FloatField(null=True)),
                ("dmi_adx", models.FloatField(null=True)),
                ("vwap", models.FloatField(null=True)),
                ("funding_rate", models.FloatField(null=True)),
                ("exchange_netflow", models.FloatField(null=True)),
                ("nupl", models.FloatField(null=True)),
                ("mayer_multiple", models.FloatField(null=True)),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="technical_analysis",
                        to="CryptoAnalyst.token",
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
        migrations.CreateModel(
            name="MarketData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("price", models.FloatField()),
                ("volume", models.FloatField(null=True)),
                ("price_change_24h", models.FloatField(null=True)),
                ("price_change_percent_24h", models.FloatField(null=True)),
                ("high_24h", models.FloatField(null=True)),
                ("low_24h", models.FloatField(null=True)),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="market_data",
                        to="CryptoAnalyst.token",
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
        migrations.CreateModel(
            name="AnalysisReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("trend_up_probability", models.IntegerField(default=0)),
                ("trend_sideways_probability", models.IntegerField(default=0)),
                ("trend_down_probability", models.IntegerField(default=0)),
                ("trend_summary", models.TextField(blank=True)),
                ("rsi_analysis", models.TextField(blank=True)),
                ("rsi_support_trend", models.CharField(blank=True, max_length=20)),
                ("macd_analysis", models.TextField(blank=True)),
                ("macd_support_trend", models.CharField(blank=True, max_length=20)),
                ("bollinger_analysis", models.TextField(blank=True)),
                (
                    "bollinger_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("bias_analysis", models.TextField(blank=True)),
                ("bias_support_trend", models.CharField(blank=True, max_length=20)),
                ("psy_analysis", models.TextField(blank=True)),
                ("psy_support_trend", models.CharField(blank=True, max_length=20)),
                ("dmi_analysis", models.TextField(blank=True)),
                ("dmi_support_trend", models.CharField(blank=True, max_length=20)),
                ("vwap_analysis", models.TextField(blank=True)),
                ("vwap_support_trend", models.CharField(blank=True, max_length=20)),
                ("funding_rate_analysis", models.TextField(blank=True)),
                (
                    "funding_rate_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("exchange_netflow_analysis", models.TextField(blank=True)),
                (
                    "exchange_netflow_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("nupl_analysis", models.TextField(blank=True)),
                ("nupl_support_trend", models.CharField(blank=True, max_length=20)),
                ("mayer_multiple_analysis", models.TextField(blank=True)),
                (
                    "mayer_multiple_support_trend",
                    models.CharField(blank=True, max_length=20),
                ),
                ("trading_action", models.CharField(default="等待", max_length=20)),
                ("trading_reason", models.TextField(blank=True)),
                ("entry_price", models.FloatField(default=0)),
                ("stop_loss", models.FloatField(default=0)),
                ("take_profit", models.FloatField(default=0)),
                ("risk_level", models.CharField(default="中", max_length=10)),
                ("risk_score", models.IntegerField(default=50)),
                ("risk_details", models.JSONField(default=list)),
                (
                    "technical_analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analysis_reports",
                        to="CryptoAnalyst.technicalanalysis",
                    ),
                ),
                (
                    "token",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analysis_reports",
                        to="CryptoAnalyst.token",
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "get_latest_by": "timestamp",
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        max_length=150, unique=True, verbose_name="用户名"
                    ),
                ),
                (
                    "email",
                    models.EmailField(max_length=254, unique=True, verbose_name="邮箱"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="是否激活"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="crypto_user_set",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "invitation_code",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="registered_users",
                        to="CryptoAnalyst.invitationcode",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="crypto_user_set",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
            },
        ),
        migrations.AddField(
            model_name="invitationcode",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_invitation_codes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="invitationcode",
            name="used_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="used_invitation_code",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
